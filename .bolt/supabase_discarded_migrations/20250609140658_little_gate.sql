/*
  # Create sample data for medical spa job management

  1. Sample Data
    - Create admin user in auth.users
    - Insert sample admin profile
    - Insert sample candidates with various statuses
    - Create sample notes and timeline events

  2. Functions
    - Function to create admin user
    - Function to generate sample candidates
*/

-- Insert sample admin into auth.users (this would normally be done through Supabase Auth)
-- For demo purposes, we'll create the admin profile directly

-- Insert admin profile
INSERT INTO admins (id, username, name, job_title, email, phone) VALUES 
(
  'admin-user-id',
  'admin',
  '<PERSON>',
  'HR Manager',
  '<EMAIL>',
  '(*************'
) ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  name = EXCLUDED.name,
  job_title = EXCLUDED.job_title,
  email = EXCLUDED.email,
  phone = EXCLUDED.phone;

-- Insert sample candidates
INSERT INTO candidates (id, first_name, last_name, email, phone, position, store_location, availability, flagged, status, created_at, updated_at) VALUES 
(
  'candidate-1',
  '<PERSON>',
  '<PERSON>',
  '<EMAIL>',
  '(*************',
  'Massage Therapist',
  'Downtown',
  ARRAY['Full-time', 'Weekends'],
  false,
  'In Progress',
  NOW() - INTERVAL '2 days',
  NOW()
),
(
  'candidate-2',
  'Michael',
  'Chen',
  '<EMAIL>',
  '(*************',
  'Esthetician',
  'West Side',
  ARRAY['Part-time', 'Evenings'],
  true,
  'Hired',
  NOW() - INTERVAL '5 days',
  NOW()
),
(
  'candidate-3',
  'Emma',
  'Davis',
  '<EMAIL>',
  '(*************',
  'Spa Receptionist',
  'North End',
  ARRAY['Full-time'],
  false,
  'On Hold',
  NOW() - INTERVAL '1 day',
  NOW()
),
(
  'candidate-4',
  'David',
  'Wilson',
  '<EMAIL>',
  '(*************',
  'Spa Manager',
  'Eastlake',
  ARRAY['Full-time', 'Flexible'],
  true,
  'In Progress',
  NOW() - INTERVAL '3 days',
  NOW()
),
(
  'candidate-5',
  'Lisa',
  'Anderson',
  '<EMAIL>',
  '(*************',
  'Nail Technician',
  'Downtown',
  ARRAY['Part-time', 'Weekends'],
  false,
  'Declined',
  NOW() - INTERVAL '7 days',
  NOW()
),
(
  'candidate-6',
  'James',
  'Brown',
  '<EMAIL>',
  '(*************',
  'Massage Therapist',
  'West Side',
  ARRAY['Evenings', 'Flexible'],
  false,
  'Archived',
  NOW() - INTERVAL '10 days',
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  email = EXCLUDED.email,
  phone = EXCLUDED.phone,
  position = EXCLUDED.position,
  store_location = EXCLUDED.store_location,
  availability = EXCLUDED.availability,
  flagged = EXCLUDED.flagged,
  status = EXCLUDED.status,
  updated_at = NOW();

-- Insert sample timeline events
INSERT INTO timeline_events (candidate_id, admin_id, title, description, type, created_at) VALUES 
('candidate-1', 'admin-user-id', 'Application Submitted', 'Sarah Johnson submitted application for Massage Therapist', 'status_change', NOW() - INTERVAL '2 days'),
('candidate-2', 'admin-user-id', 'Application Submitted', 'Michael Chen submitted application for Esthetician', 'status_change', NOW() - INTERVAL '5 days'),
('candidate-2', 'admin-user-id', 'Interview Scheduled', 'First interview scheduled with Michael', 'interview_scheduled', NOW() - INTERVAL '3 days'),
('candidate-2', 'admin-user-id', 'Status Changed to Hired', 'Application status updated to Hired', 'status_change', NOW() - INTERVAL '1 day'),
('candidate-3', 'admin-user-id', 'Application Submitted', 'Emma Davis submitted application for Spa Receptionist', 'status_change', NOW() - INTERVAL '1 day'),
('candidate-4', 'admin-user-id', 'Application Submitted', 'David Wilson submitted application for Spa Manager', 'status_change', NOW() - INTERVAL '3 days'),
('candidate-5', 'admin-user-id', 'Application Submitted', 'Lisa Anderson submitted application for Nail Technician', 'status_change', NOW() - INTERVAL '7 days'),
('candidate-5', 'admin-user-id', 'Status Changed to Declined', 'Application status updated to Declined', 'status_change', NOW() - INTERVAL '5 days'),
('candidate-6', 'admin-user-id', 'Application Submitted', 'James Brown submitted application for Massage Therapist', 'status_change', NOW() - INTERVAL '10 days'),
('candidate-6', 'admin-user-id', 'Status Changed to Archived', 'Application status updated to Archived', 'status_change', NOW() - INTERVAL '8 days')
ON CONFLICT (id) DO NOTHING;

-- Insert sample notes
INSERT INTO notes (candidate_id, admin_id, content, created_at) VALUES 
('candidate-1', 'admin-user-id', 'Candidate has excellent communication skills and previous spa experience.', NOW() - INTERVAL '1 day'),
('candidate-2', 'admin-user-id', 'Great interview performance. Strong technical skills.', NOW() - INTERVAL '2 days'),
('candidate-2', 'admin-user-id', 'References checked - all positive feedback.', NOW() - INTERVAL '1 day'),
('candidate-3', 'admin-user-id', 'Need to follow up on availability for weekend shifts.', NOW() - INTERVAL '12 hours'),
('candidate-4', 'admin-user-id', 'Management experience looks promising. Schedule interview soon.', NOW() - INTERVAL '2 days')
ON CONFLICT (id) DO NOTHING;