/*
  # Create candidates management system

  1. New Tables
    - `admins`
      - `id` (uuid, primary key)
      - `username` (text, unique)
      - `name` (text)
      - `job_title` (text)
      - `email` (text, unique)
      - `phone` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    - `candidates`
      - `id` (uuid, primary key)
      - `first_name` (text)
      - `last_name` (text)
      - `email` (text, unique)
      - `phone` (text)
      - `position` (text)
      - `store_location` (text)
      - `availability` (text array)
      - `resume_url` (text)
      - `status` (text, default 'pending')
      - `flagged` (boolean, default false)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    - `notes`
      - `id` (uuid, primary key)
      - `candidate_id` (uuid, foreign key)
      - `admin_id` (uuid, foreign key)
      - `content` (text)
      - `created_at` (timestamp)
    - `timeline_events`
      - `id` (uuid, primary key)
      - `candidate_id` (uuid, foreign key)
      - `admin_id` (uuid, foreign key)
      - `title` (text)
      - `description` (text)
      - `type` (text)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage data
    - Allow anonymous users to insert candidates (for application form)

  3. Sample Data
    - Insert sample admin user
    - Insert sample candidates for testing
*/

-- Create admins table
CREATE TABLE IF NOT EXISTS admins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  username text UNIQUE NOT NULL,
  name text NOT NULL,
  job_title text DEFAULT '',
  email text UNIQUE NOT NULL,
  phone text DEFAULT '',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create candidates table
CREATE TABLE IF NOT EXISTS candidates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name text NOT NULL,
  last_name text NOT NULL,
  email text UNIQUE NOT NULL,
  phone text NOT NULL,
  position text NOT NULL,
  store_location text NOT NULL,
  availability text[] DEFAULT '{}',
  resume_url text DEFAULT '',
  status text DEFAULT 'pending',
  flagged boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create notes table
CREATE TABLE IF NOT EXISTS notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  candidate_id uuid REFERENCES candidates(id) ON DELETE CASCADE,
  admin_id uuid REFERENCES admins(id) ON DELETE CASCADE,
  content text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create timeline_events table
CREATE TABLE IF NOT EXISTS timeline_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  candidate_id uuid REFERENCES candidates(id) ON DELETE CASCADE,
  admin_id uuid REFERENCES admins(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text DEFAULT '',
  type text DEFAULT 'general',
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for admins table
CREATE POLICY "Admins can read own data"
  ON admins
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can update own data"
  ON admins
  FOR UPDATE
  TO authenticated
  USING (true);

-- RLS Policies for candidates table
CREATE POLICY "Anyone can insert candidates"
  ON candidates
  FOR INSERT
  TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can read candidates"
  ON candidates
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can update candidates"
  ON candidates
  FOR UPDATE
  TO authenticated
  USING (true);

-- RLS Policies for notes table
CREATE POLICY "Authenticated users can read notes"
  ON notes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert notes"
  ON notes
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can delete notes"
  ON notes
  FOR DELETE
  TO authenticated
  USING (true);

-- RLS Policies for timeline_events table
CREATE POLICY "Authenticated users can read timeline events"
  ON timeline_events
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert timeline events"
  ON timeline_events
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Insert sample admin user
INSERT INTO admins (id, username, name, job_title, email, phone) 
VALUES (
  'admin-user-id',
  'admin',
  'Admin User',
  'System Administrator',
  '<EMAIL>',
  '(*************'
) ON CONFLICT (id) DO NOTHING;

-- Insert sample candidates
INSERT INTO candidates (first_name, last_name, email, phone, position, store_location, availability, status, flagged) VALUES
  ('Sarah', 'Johnson', '<EMAIL>', '(*************', 'Esthetician', 'Downtown', ARRAY['Monday', 'Tuesday', 'Wednesday'], 'pending', false),
  ('Michael', 'Chen', '<EMAIL>', '(*************', 'Massage Therapist', 'Westside', ARRAY['Thursday', 'Friday', 'Saturday'], 'interview', true),
  ('Emily', 'Rodriguez', '<EMAIL>', '(*************', 'Receptionist', 'Downtown', ARRAY['Monday', 'Wednesday', 'Friday'], 'hired', false),
  ('David', 'Thompson', '<EMAIL>', '(*************', 'Esthetician', 'Eastside', ARRAY['Tuesday', 'Thursday', 'Sunday'], 'rejected', false),
  ('Jessica', 'Williams', '<EMAIL>', '(*************', 'Nail Technician', 'Westside', ARRAY['Monday', 'Tuesday', 'Thursday'], 'pending', false),
  ('Ryan', 'Davis', '<EMAIL>', '(*************', 'Massage Therapist', 'Downtown', ARRAY['Wednesday', 'Friday', 'Saturday'], 'interview', false),
  ('Amanda', 'Miller', '<EMAIL>', '(*************', 'Receptionist', 'Eastside', ARRAY['Monday', 'Thursday', 'Friday'], 'pending', true),
  ('Christopher', 'Wilson', '<EMAIL>', '(*************', 'Esthetician', 'Westside', ARRAY['Tuesday', 'Wednesday', 'Saturday'], 'hired', false)
ON CONFLICT (email) DO NOTHING;

-- Insert sample notes
INSERT INTO notes (candidate_id, admin_id, content) 
SELECT 
  c.id,
  'admin-user-id',
  CASE 
    WHEN c.first_name = 'Sarah' THEN 'Great portfolio and experience with advanced skincare treatments.'
    WHEN c.first_name = 'Michael' THEN 'Excellent references from previous spa. Flagged for immediate follow-up.'
    WHEN c.first_name = 'Emily' THEN 'Perfect fit for our front desk team. Very professional demeanor.'
    WHEN c.first_name = 'David' THEN 'Skills didn''t match our current needs, but keep on file for future openings.'
    WHEN c.first_name = 'Jessica' THEN 'Strong nail art portfolio. Available for weekend shifts.'
    WHEN c.first_name = 'Ryan' THEN 'Certified in deep tissue massage. Scheduling phone interview.'
    WHEN c.first_name = 'Amanda' THEN 'Bilingual candidate - great for our diverse clientele. Flagged for priority review.'
    WHEN c.first_name = 'Christopher' THEN 'Hired for our Westside location. Start date confirmed.'
  END
FROM candidates c
WHERE c.email IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ON CONFLICT DO NOTHING;

-- Insert sample timeline events
INSERT INTO timeline_events (candidate_id, admin_id, title, description, type)
SELECT 
  c.id,
  'admin-user-id',
  'Application Submitted',
  'Candidate submitted their application online',
  'application_submitted'
FROM candidates c
ON CONFLICT DO NOTHING;

-- Add status change events for non-pending candidates
INSERT INTO timeline_events (candidate_id, admin_id, title, description, type)
SELECT 
  c.id,
  'admin-user-id',
  'Status Updated',
  'Status changed to ' || c.status,
  'status_change'
FROM candidates c
WHERE c.status != 'pending'
ON CONFLICT DO NOTHING;