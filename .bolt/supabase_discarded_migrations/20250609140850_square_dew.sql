/*
  # Remove all sample data

  1. Clean up existing data
    - Remove all sample candidates
    - Remove all sample timeline events  
    - Remove all sample notes
    - Remove sample admin data

  2. Reset tables to empty state
    - Keep table structure intact
    - Remove all test/sample records
*/

-- Remove all sample timeline events
DELETE FROM timeline_events;

-- Remove all sample notes
DELETE FROM notes;

-- Remove all sample candidates
DELETE FROM candidates;

-- Remove sample admin (keep table structure)
DELETE FROM admins WHERE id = 'admin-user-id';

-- Reset sequences if any exist
-- Note: UUID primary keys don't use sequences, so this is just for completeness