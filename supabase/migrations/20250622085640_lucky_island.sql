/*
  # Connect to real Supabase authentication

  1. Database Changes
    - Update foreign key constraints to reference auth.users
    - Update RLS policies to use auth.uid()
    - Clean up mock data
    - Add automatic admin profile creation

  2. Security
    - Proper RLS policies using auth.uid()
    - Foreign keys to auth.users for data integrity
    - Automatic admin profile creation for new users
*/

-- First, drop all dependent foreign key constraints
ALTER TABLE notes DROP CONSTRAINT IF EXISTS notes_admin_id_fkey;
ALTER TABLE timeline_events DROP CONSTRAINT IF EXISTS timeline_events_admin_id_fkey;

-- Now we can safely modify the admins table primary key if needed
-- (Actually, we don't need to drop and recreate the primary key, just add the foreign key)

-- Add foreign key constraint to auth.users if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE table_name = 'admins' 
    AND constraint_name = 'admins_id_fkey'
  ) THEN
    ALTER TABLE admins 
    ADD CONSTRAINT admins_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Re-add foreign key constraints for notes and timeline_events to reference auth.users
ALTER TABLE notes 
ADD CONSTRAINT notes_admin_id_fkey 
FOREIGN KEY (admin_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE timeline_events 
ADD CONSTRAINT timeline_events_admin_id_fkey 
FOREIGN KEY (admin_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Update RLS policies for admins table to use auth.uid()
DROP POLICY IF EXISTS "Admins can read own data" ON admins;
DROP POLICY IF EXISTS "Admins can update own data" ON admins;

CREATE POLICY "Users can read own admin profile"
  ON admins
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own admin profile"
  ON admins
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own admin profile"
  ON admins
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Update RLS policies for notes to use proper authentication
DROP POLICY IF EXISTS "Authenticated users can read notes" ON notes;
DROP POLICY IF EXISTS "Authenticated users can insert notes" ON notes;
DROP POLICY IF EXISTS "Authenticated users can delete notes" ON notes;

CREATE POLICY "Authenticated users can read all notes"
  ON notes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert notes"
  ON notes
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = admin_id);

CREATE POLICY "Authenticated users can delete own notes"
  ON notes
  FOR DELETE
  TO authenticated
  USING (auth.uid() = admin_id);

-- Update RLS policies for timeline_events to use proper authentication
DROP POLICY IF EXISTS "Authenticated users can read timeline events" ON timeline_events;
DROP POLICY IF EXISTS "Authenticated users can insert timeline events" ON timeline_events;

CREATE POLICY "Authenticated users can read all timeline events"
  ON timeline_events
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert timeline events"
  ON timeline_events
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = admin_id);

-- Clean up any existing mock data that doesn't reference real users
DELETE FROM timeline_events WHERE admin_id NOT IN (SELECT id FROM auth.users);
DELETE FROM notes WHERE admin_id NOT IN (SELECT id FROM auth.users);
DELETE FROM admins WHERE id NOT IN (SELECT id FROM auth.users);

-- Create function to automatically create admin profile for new users
CREATE OR REPLACE FUNCTION create_admin_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO admins (id, username, name, job_title, email, phone)
  VALUES (
    NEW.id,
    COALESCE(NEW.email, 'user'),
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'Admin User'),
    'Administrator',
    COALESCE(NEW.email, ''),
    COALESCE(NEW.raw_user_meta_data->>'phone', '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create admin profile for new users
DROP TRIGGER IF EXISTS create_admin_profile_trigger ON auth.users;
CREATE TRIGGER create_admin_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_admin_profile();

-- Update existing policies to ensure proper access for candidates
DROP POLICY IF EXISTS "Authenticated users can read candidates" ON candidates;
DROP POLICY IF EXISTS "Authenticated users can update candidates" ON candidates;
DROP POLICY IF EXISTS "Anyone can insert candidates" ON candidates;
DROP POLICY IF EXISTS "Authenticated users can manage candidates" ON candidates;
DROP POLICY IF EXISTS "Anonymous users can submit applications" ON candidates;

CREATE POLICY "Authenticated users can manage candidates"
  ON candidates
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Anonymous users can submit applications"
  ON candidates
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Ensure proper indexes exist for performance
CREATE INDEX IF NOT EXISTS idx_admins_email ON admins(email);
CREATE INDEX IF NOT EXISTS idx_notes_admin_id ON notes(admin_id);
CREATE INDEX IF NOT EXISTS idx_timeline_events_admin_id ON timeline_events(admin_id);