/*
  # Align database schema with app expectations

  1. Schema Alignment
    - Add missing columns that the app expects
    - Handle enum type conversions properly
    - Migrate existing data safely
    - Add proper constraints and indexes

  2. Data Migration
    - Convert status values to match app expectations
    - Migrate is_new to flagged column
    - Ensure all required fields have values

  3. Security and Performance
    - Update RLS policies
    - Add indexes for better performance
    - Create missing tables and relationships
*/

-- First, let's add the missing columns that the app expects
DO $$
BEGIN
  -- Add flagged column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'flagged'
  ) THEN
    ALTER TABLE candidates ADD COLUMN flagged boolean DEFAULT false;
  END IF;

  -- Add updated_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE candidates ADD COLUMN updated_at timestamptz DEFAULT now();
  END IF;

  -- Add first_name column with default if it doesn't exist (it should exist based on schema)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'first_name'
  ) THEN
    ALTER TABLE candidates ADD COLUMN first_name text DEFAULT '' NOT NULL;
  END IF;
END $$;

-- Migrate data from is_new to flagged if is_new column exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'is_new'
  ) THEN
    -- Set flagged to true for candidates that are NOT new (inverse logic)
    UPDATE candidates SET flagged = NOT is_new WHERE is_new IS NOT NULL;
  END IF;
END $$;

-- Update status values to match app expectations (handling enum type properly)
DO $$
BEGIN
  -- Check if status column is of enum type
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' 
    AND column_name = 'status' 
    AND data_type = 'USER-DEFINED'
  ) THEN
    -- Handle enum type - cast text to enum
    UPDATE candidates 
    SET status = CASE 
      WHEN status::text = 'new' THEN 'in_progress'::application_status
      WHEN status::text = 'in_progress' THEN 'in_progress'::application_status
      WHEN status::text = 'on_hold' THEN 'on_hold'::application_status
      WHEN status::text = 'declined' THEN 'declined'::application_status
      WHEN status::text = 'hired' THEN 'hired'::application_status
      WHEN status::text = 'archived' THEN 'archived'::application_status
      ELSE 'in_progress'::application_status
    END;
  ELSE
    -- Handle text type
    UPDATE candidates 
    SET status = CASE 
      WHEN status = 'new' THEN 'In Progress'
      WHEN status = 'in_progress' THEN 'In Progress'
      WHEN status = 'on_hold' THEN 'On Hold'
      WHEN status = 'declined' THEN 'Declined'
      WHEN status = 'hired' THEN 'Hired'
      WHEN status = 'archived' THEN 'Archived'
      ELSE 'In Progress'
    END;
  END IF;
END $$;

-- Ensure all candidates have proper default values
UPDATE candidates 
SET 
  flagged = COALESCE(flagged, false),
  updated_at = COALESCE(updated_at, created_at, now()),
  first_name = COALESCE(first_name, ''),
  availability = COALESCE(availability, '{}');

-- Add missing foreign key constraints if they don't exist
DO $$
BEGIN
  -- Check if position_id foreign key exists and add positions table reference
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'position_id'
  ) THEN
    -- If position_id exists but positions table doesn't, we'll work with the text position field
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.tables
      WHERE table_name = 'positions'
    ) THEN
      -- Remove position_id constraint if positions table doesn't exist
      ALTER TABLE candidates DROP COLUMN IF EXISTS position_id;
    END IF;
  END IF;

  -- Check if location_id foreign key exists and add locations table reference
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'location_id'
  ) THEN
    -- If location_id exists but locations table doesn't, we'll work with the text location field
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.tables
      WHERE table_name = 'locations'
    ) THEN
      -- Remove location_id constraint if locations table doesn't exist
      ALTER TABLE candidates DROP COLUMN IF EXISTS location_id;
    END IF;
  END IF;
END $$;

-- Add missing columns to candidates table that the app expects
DO $$
BEGIN
  -- Add store_location if it doesn't exist (app expects this field)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'store_location'
  ) THEN
    ALTER TABLE candidates ADD COLUMN store_location text DEFAULT '';
  END IF;

  -- Add position as text if it doesn't exist (app expects this field)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'position'
  ) THEN
    ALTER TABLE candidates ADD COLUMN position text DEFAULT '';
  END IF;

  -- Add resume_url if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'resume_url'
  ) THEN
    ALTER TABLE candidates ADD COLUMN resume_url text;
  END IF;
END $$;

-- Create missing tables that the app expects
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table
CREATE POLICY "Allow authenticated users to read users"
  ON users
  FOR SELECT
  TO authenticated
  USING (true);

-- Update admin_profiles table to reference users table properly
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'admin_profiles'
  ) THEN
    -- Ensure admin_profiles has proper foreign key to users
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints
      WHERE table_name = 'admin_profiles' 
      AND constraint_name = 'admin_profiles_id_fkey'
    ) THEN
      -- Insert corresponding user records for existing admin profiles
      INSERT INTO users (id, email)
      SELECT ap.id, ap.email
      FROM admin_profiles ap
      WHERE NOT EXISTS (SELECT 1 FROM users u WHERE u.id = ap.id)
      ON CONFLICT (id) DO NOTHING;
      
      -- Add foreign key constraint
      ALTER TABLE admin_profiles 
      ADD CONSTRAINT admin_profiles_id_fkey 
      FOREIGN KEY (id) REFERENCES users(id);
    END IF;
  END IF;
END $$;

-- Update admin_preferences table to reference users table properly
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'admin_preferences'
  ) THEN
    -- Ensure admin_preferences has proper foreign key to users
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints
      WHERE table_name = 'admin_preferences' 
      AND constraint_name = 'admin_preferences_admin_id_fkey'
    ) THEN
      -- Insert corresponding user records for existing admin preferences
      INSERT INTO users (id, email)
      SELECT ap.admin_id, COALESCE(u.email, '<EMAIL>')
      FROM admin_preferences ap
      LEFT JOIN admin_profiles u ON u.id = ap.admin_id
      WHERE NOT EXISTS (SELECT 1 FROM users us WHERE us.id = ap.admin_id)
      ON CONFLICT (id) DO NOTHING;
      
      -- Add foreign key constraint
      ALTER TABLE admin_preferences 
      ADD CONSTRAINT admin_preferences_admin_id_fkey 
      FOREIGN KEY (admin_id) REFERENCES users(id);
    END IF;
  END IF;
END $$;

-- Add indexes for better performance on frequently queried columns
CREATE INDEX IF NOT EXISTS idx_candidates_status ON candidates(status);
CREATE INDEX IF NOT EXISTS idx_candidates_flagged ON candidates(flagged);
CREATE INDEX IF NOT EXISTS idx_candidates_created_at ON candidates(created_at);
CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email);
CREATE INDEX IF NOT EXISTS idx_notes_candidate_id ON notes(candidate_id);
CREATE INDEX IF NOT EXISTS idx_timeline_events_candidate_id ON timeline_events(candidate_id);

-- Update RLS policies to match app expectations
DROP POLICY IF EXISTS "Allow authenticated full access" ON candidates;
DROP POLICY IF EXISTS "Admins can modify all candidates" ON candidates;
DROP POLICY IF EXISTS "Admins can read all candidates" ON candidates;

-- Create comprehensive RLS policies for candidates
CREATE POLICY "Allow authenticated users to read candidates"
  ON candidates
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to update candidates"
  ON candidates
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to insert candidates"
  ON candidates
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow anonymous users to insert candidates"
  ON candidates
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Update RLS policies for notes
DROP POLICY IF EXISTS "Allow authenticated full access" ON notes;
DROP POLICY IF EXISTS "Admins can create notes" ON notes;
DROP POLICY IF EXISTS "Admins can read all notes" ON notes;

CREATE POLICY "Allow authenticated users to read notes"
  ON notes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to insert notes"
  ON notes
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Update RLS policies for timeline_events
DROP POLICY IF EXISTS "Allow authenticated full access" ON timeline_events;
DROP POLICY IF EXISTS "Admins can create timeline events" ON timeline_events;
DROP POLICY IF EXISTS "Admins can read all timeline events" ON timeline_events;

CREATE POLICY "Allow authenticated users to read timeline events"
  ON timeline_events
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to insert timeline events"
  ON timeline_events
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Create trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger to automatically update updated_at on candidates table
DROP TRIGGER IF EXISTS update_candidates_updated_at ON candidates;
CREATE TRIGGER update_candidates_updated_at
  BEFORE UPDATE ON candidates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Ensure all required columns have proper constraints
DO $$
BEGIN
  -- Only set NOT NULL if the column exists and has values
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'first_name'
  ) THEN
    ALTER TABLE candidates ALTER COLUMN first_name SET NOT NULL;
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'last_name'
  ) THEN
    ALTER TABLE candidates ALTER COLUMN last_name SET NOT NULL;
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'email'
  ) THEN
    ALTER TABLE candidates ALTER COLUMN email SET NOT NULL;
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' AND column_name = 'flagged'
  ) THEN
    ALTER TABLE candidates ALTER COLUMN flagged SET DEFAULT false;
  END IF;
END $$;

-- Set default for status column based on its type
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' 
    AND column_name = 'status' 
    AND data_type = 'USER-DEFINED'
  ) THEN
    -- For enum type, set default to enum value
    ALTER TABLE candidates ALTER COLUMN status SET DEFAULT 'in_progress'::application_status;
  ELSE
    -- For text type, set default to text value
    ALTER TABLE candidates ALTER COLUMN status SET DEFAULT 'In Progress';
  END IF;
END $$;

-- Add unique constraint on email if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE table_name = 'candidates' 
    AND constraint_name = 'candidates_email_key'
  ) THEN
    ALTER TABLE candidates ADD CONSTRAINT candidates_email_key UNIQUE (email);
  END IF;
END $$;

-- Clean up any orphaned data and ensure data integrity
UPDATE candidates 
SET 
  first_name = COALESCE(NULLIF(first_name, ''), 'Unknown'),
  last_name = COALESCE(NULLIF(last_name, ''), 'Candidate'),
  email = COALESCE(NULLIF(email, ''), '<EMAIL>'),
  flagged = COALESCE(flagged, false),
  availability = COALESCE(availability, '{}'),
  created_at = COALESCE(created_at, now()),
  updated_at = COALESCE(updated_at, now());

-- Handle status field cleanup based on type
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'candidates' 
    AND column_name = 'status' 
    AND data_type = 'USER-DEFINED'
  ) THEN
    -- For enum type
    UPDATE candidates 
    SET status = 'in_progress'::application_status
    WHERE status IS NULL;
  ELSE
    -- For text type
    UPDATE candidates 
    SET status = COALESCE(NULLIF(status, ''), 'In Progress');
  END IF;
END $$;