/*
  # Fix user creation database issues

  1. Database Fixes
    - Remove password_hash column from admins table (not needed with Supabase auth)
    - Fix foreign key constraints that might be causing issues
    - Update RLS policies to allow proper user creation
    - Add missing trigger functions

  2. Security
    - Ensure proper RLS policies for user creation
    - Fix admin profile creation process
*/

-- Remove password_hash column from admins table since we use Supabase auth
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'admins' AND column_name = 'password_hash'
  ) THEN
    ALTER TABLE admins DROP COLUMN password_hash;
  END IF;
END $$;

-- Ensure the admin profile creation trigger function exists and works correctly
CREATE OR REPLACE FUNCTION create_admin_profile()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO admins (id, username, name, job_title, email, phone)
  VALUES (
    NEW.id,
    COALESCE(NEW.email, 'user'),
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'Admin User'),
    'Administrator',
    COALESCE(NEW.email, ''),
    COALESCE(NEW.raw_user_meta_data->>'phone', '')
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = now();
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create admin profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger to ensure it's working
DROP TRIGGER IF EXISTS create_admin_profile_trigger ON auth.users;
CREATE TRIGGER create_admin_profile_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_admin_profile();

-- Update RLS policies to be more permissive for admin creation
DROP POLICY IF EXISTS "Users can insert own admin profile" ON admins;
DROP POLICY IF EXISTS "Users can read own admin profile" ON admins;
DROP POLICY IF EXISTS "Users can update own admin profile" ON admins;

-- Create more permissive policies for admin profiles
CREATE POLICY "Users can manage their own admin profile"
  ON admins
  FOR ALL
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Allow the trigger function to insert admin profiles
CREATE POLICY "Allow admin profile creation via trigger"
  ON admins
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Ensure the updated_at trigger function exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists on admin_profiles table if it exists
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'admin_profiles'
  ) THEN
    DROP TRIGGER IF EXISTS update_admin_profiles_updated_at ON admin_profiles;
    CREATE TRIGGER update_admin_profiles_updated_at
      BEFORE UPDATE ON admin_profiles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- Add trigger to admins table
DROP TRIGGER IF EXISTS update_admins_updated_at ON admins;
CREATE TRIGGER update_admins_updated_at
  BEFORE UPDATE ON admins
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Ensure username is unique but allow updates
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE table_name = 'admins' 
    AND constraint_name = 'admins_username_key'
  ) THEN
    ALTER TABLE admins ADD CONSTRAINT admins_username_key UNIQUE (username);
  END IF;
EXCEPTION
  WHEN duplicate_table THEN
    -- Constraint already exists, ignore
    NULL;
END $$;

-- Clean up any potential data issues
UPDATE admins 
SET 
  username = COALESCE(NULLIF(username, ''), email, 'admin'),
  name = COALESCE(NULLIF(name, ''), 'Admin User'),
  job_title = COALESCE(NULLIF(job_title, ''), 'Administrator'),
  email = COALESCE(NULLIF(email, ''), '<EMAIL>'),
  updated_at = now()
WHERE username IS NULL OR username = '' OR name IS NULL OR name = '';

-- Ensure all required columns have proper defaults
ALTER TABLE admins ALTER COLUMN username SET DEFAULT 'admin';
ALTER TABLE admins ALTER COLUMN name SET DEFAULT 'Admin User';
ALTER TABLE admins ALTER COLUMN job_title SET DEFAULT 'Administrator';
ALTER TABLE admins ALTER COLUMN email SET DEFAULT '';
ALTER TABLE admins ALTER COLUMN phone SET DEFAULT '';