/*
  # Initial schema setup for medical spa job management

  1. New Tables
    - `admins`
      - `id` (uuid, primary key)
      - `username` (text, unique)
      - `password_hash` (text)
      - `name` (text)
      - `job_title` (text)
      - `email` (text)
      - `phone` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `candidates`
      - `id` (uuid, primary key)
      - `first_name` (text)
      - `last_name` (text)
      - `email` (text)
      - `phone` (text)
      - `position` (text)
      - `store_location` (text)
      - `availability` (text[])
      - `resume_url` (text)
      - `liked` (boolean)
      - `rating` (integer)
      - `status` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `notes`
      - `id` (uuid, primary key)
      - `candidate_id` (uuid, foreign key)
      - `admin_id` (uuid, foreign key)
      - `content` (text)
      - `created_at` (timestamptz)

    - `timeline_events`
      - `id` (uuid, primary key)
      - `candidate_id` (uuid, foreign key)
      - `admin_id` (uuid, foreign key)
      - `title` (text)
      - `description` (text)
      - `type` (text)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Admins table
CREATE TABLE IF NOT EXISTS admins (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  username text UNIQUE NOT NULL,
  password_hash text NOT NULL,
  name text NOT NULL,
  job_title text,
  email text,
  phone text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Candidates table
CREATE TABLE IF NOT EXISTS candidates (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  first_name text NOT NULL,
  last_name text NOT NULL,
  email text NOT NULL,
  phone text,
  position text NOT NULL,
  store_location text,
  availability text[],
  resume_url text,
  liked boolean DEFAULT false,
  rating integer DEFAULT 0,
  status text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Notes table
CREATE TABLE IF NOT EXISTS notes (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id uuid REFERENCES candidates(id) ON DELETE CASCADE,
  admin_id uuid REFERENCES admins(id),
  content text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Timeline events table
CREATE TABLE IF NOT EXISTS timeline_events (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id uuid REFERENCES candidates(id) ON DELETE CASCADE,
  admin_id uuid REFERENCES admins(id),
  title text NOT NULL,
  description text,
  type text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_events ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Admins can read own data"
  ON admins
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admins can update own data"
  ON admins
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Admins can read all candidates"
  ON candidates
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can modify all candidates"
  ON candidates
  FOR ALL
  TO authenticated
  USING (true);

CREATE POLICY "Admins can read all notes"
  ON notes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can create notes"
  ON notes
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Admins can read all timeline events"
  ON timeline_events
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can create timeline events"
  ON timeline_events
  FOR INSERT
  TO authenticated
  WITH CHECK (true);