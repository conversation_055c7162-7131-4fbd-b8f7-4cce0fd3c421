import { supabase } from "../lib/supabase";
import {
  Api<PERSON>andidate,
  ApiNote,
  ApiTimelineEvent,
  ApiAdmin,
} from "../types/api";
import { API_CONFIG } from "../config/constants";

/**
 * Service for handling all API interactions with Supabase
 * Provides methods for authentication, candidate management, notes, and timeline events
 */
class ApiService {
  /**
   * Centralized error handling for API operations
   * @param error - Error object from API call
   * @param operation - Description of the operation that failed
   */
  private handleError(error: any, operation: string): never {
    console.error(`${operation} failed:`, error);
    throw new Error(error.message || `${operation} failed`);
  }

  /**
   * Checks if user has an active session and returns session data
   * @returns Session data or null if not authenticated
   */
  private async getAuthenticatedSession() {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session;
  }

  // Authentication Methods

  /**
   * Signs in a user with email and password
   * @param email - User's email address
   * @param password - User's password
   * @returns Authentication result with user data
   */
  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Provide user-friendly error messages
        throw new Error(this.getAuthErrorMessage(error.message));
      }
      return { data, error: null };
    } catch (error) {
      this.handleError(error, "Sign in");
    }
  }

  /**
   * Signs out the current user
   * @returns Sign out result
   */
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      this.handleError(error, "Sign out");
    }
  }

  /**
   * Gets the current user session
   * @returns Current session data
   */
  async getSession() {
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      this.handleError(error, "Get session");
    }
  }

  /**
   * Converts auth error messages to user-friendly text
   * @param errorMessage - Raw error message from Supabase
   * @returns User-friendly error message
   */
  private getAuthErrorMessage(errorMessage: string): string {
    if (errorMessage.includes("Invalid login credentials")) {
      return "Invalid email or password. Please check your credentials.";
    } else if (errorMessage.includes("Email not confirmed")) {
      return "Please confirm your email address before signing in.";
    } else if (errorMessage.includes("Too many requests")) {
      return "Too many login attempts. Please wait a moment and try again.";
    }
    return errorMessage;
  }

  // Admin methods
  async getAdmin(id: string): Promise<ApiAdmin | null> {
    try {
      const { data, error } = await supabase
        .from("admins")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        // If no rows found, return null instead of throwing
        if (
          error.code === "PGRST116" ||
          error.message.includes("no rows returned")
        ) {
          return null;
        }
        throw error;
      }
      return data;
    } catch (error) {
      // Handle the case where no admin is found
      if (
        error instanceof Error &&
        (error.message.includes("no rows returned") ||
          error.message.includes("PGRST116"))
      ) {
        return null;
      }
      this.handleError(error, "Get admin");
    }
  }

  async createAdmin(
    admin: Omit<ApiAdmin, "created_at" | "updated_at">
  ): Promise<ApiAdmin> {
    try {
      const { data, error } = await supabase
        .from("admins")
        .insert([admin])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Create admin");
    }
  }

  async updateAdmin(id: string, updates: Partial<ApiAdmin>): Promise<ApiAdmin> {
    try {
      const { data, error } = await supabase
        .from("admins")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Update admin");
    }
  }

  // Candidate methods
  async getCandidates(): Promise<ApiCandidate[]> {
    try {
      console.log("🔍 Fetching candidates from Supabase...");

      // Check if user is authenticated
      const session = await this.getAuthenticatedSession();

      if (!session) {
        console.log("⚠️ No authenticated session found");
        // Return empty array instead of throwing error for unauthenticated users
        return [];
      }

      const { data, error } = await supabase
        .from("candidates")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("❌ Supabase error fetching candidates:", error);
        // Don't throw error for permission issues, return empty array
        if (
          error.code === "42501" ||
          error.message.includes("row-level security")
        ) {
          console.log(
            "⚠️ RLS policy prevented access. User may need to authenticate."
          );
          return [];
        }
        throw error;
      }

      console.log(
        "✅ Candidates fetched successfully:",
        data?.length || 0,
        "records"
      );
      console.log("📊 Sample candidate data:", data?.[0]);

      return data || [];
    } catch (error) {
      console.error("💥 Failed to fetch candidates from database:", error);
      // Return empty array instead of throwing for RLS errors
      if (
        error instanceof Error &&
        (error.message.includes("row-level security") ||
          error.message.includes("42501"))
      ) {
        console.log("⚠️ Returning empty array due to RLS policy");
        return [];
      }
      throw error;
    }
  }

  /**
   * Optimized method to fetch candidates with their notes and timeline events in batch
   * Eliminates N+1 query pattern by using batch operations
   */
  async getCandidatesWithDetails(): Promise<{
    candidates: ApiCandidate[];
    notes: Record<string, ApiNote[]>;
    timelineEvents: Record<string, ApiTimelineEvent[]>;
  }> {
    try {
      console.log("🔍 Fetching candidates with details in batch...");

      // Check if user is authenticated
      const session = await this.getAuthenticatedSession();

      if (!session) {
        console.log("⚠️ No authenticated session found");
        return { candidates: [], notes: {}, timelineEvents: {} };
      }

      // Fetch all data in parallel using Promise.all
      const [candidatesResult, notesResult, timelineEventsResult] =
        await Promise.all([
          supabase
            .from("candidates")
            .select("*")
            .order("created_at", { ascending: false }),
          supabase
            .from("notes")
            .select("*")
            .order("created_at", { ascending: false }),
          supabase
            .from("timeline_events")
            .select("*")
            .order("created_at", { ascending: false }),
        ]);

      // Handle candidates error
      if (candidatesResult.error) {
        console.error(
          "❌ Supabase error fetching candidates:",
          candidatesResult.error
        );
        if (
          candidatesResult.error.code === "42501" ||
          candidatesResult.error.message.includes("row-level security")
        ) {
          console.log(
            "⚠️ RLS policy prevented access. User may need to authenticate."
          );
          return { candidates: [], notes: {}, timelineEvents: {} };
        }
        throw candidatesResult.error;
      }

      const candidates = candidatesResult.data || [];

      // Handle notes error (non-critical, continue with empty notes)
      if (notesResult.error) {
        console.warn("⚠️ Failed to fetch notes:", notesResult.error);
      }

      // Handle timeline events error (non-critical, continue with empty events)
      if (timelineEventsResult.error) {
        console.warn(
          "⚠️ Failed to fetch timeline events:",
          timelineEventsResult.error
        );
      }

      // Group notes by candidate_id
      const notes: Record<string, ApiNote[]> = {};
      (notesResult.data || []).forEach((note) => {
        if (!notes[note.candidate_id]) {
          notes[note.candidate_id] = [];
        }
        notes[note.candidate_id].push(note);
      });

      // Group timeline events by candidate_id
      const timelineEvents: Record<string, ApiTimelineEvent[]> = {};
      (timelineEventsResult.data || []).forEach((event) => {
        if (!timelineEvents[event.candidate_id]) {
          timelineEvents[event.candidate_id] = [];
        }
        timelineEvents[event.candidate_id].push(event);
      });

      console.log(
        "✅ Candidates with details fetched successfully:",
        candidates.length,
        "candidates,",
        Object.keys(notes).length,
        "candidates with notes,",
        Object.keys(timelineEvents).length,
        "candidates with timeline events"
      );

      return { candidates, notes, timelineEvents };
    } catch (error) {
      console.error("💥 Failed to fetch candidates with details:", error);
      // Return empty data instead of throwing for RLS errors
      if (
        error instanceof Error &&
        (error.message.includes("row-level security") ||
          error.message.includes("42501"))
      ) {
        console.log("⚠️ Returning empty data due to RLS policy");
        return { candidates: [], notes: {}, timelineEvents: {} };
      }
      throw error;
    }
  }

  async getCandidate(id: string): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from("candidates")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Get candidate");
    }
  }

  async createCandidate(
    candidate: Omit<ApiCandidate, "id" | "created_at" | "updated_at">
  ): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from("candidates")
        .insert([candidate])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Create candidate");
    }
  }

  async updateCandidate(
    id: string,
    updates: Partial<ApiCandidate>
  ): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from("candidates")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Update candidate");
    }
  }

  // Notes methods
  async getNotes(candidateId: string): Promise<ApiNote[]> {
    try {
      const { data, error } = await supabase
        .from("notes")
        .select("*")
        .eq("candidate_id", candidateId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.warn(
        `Failed to fetch notes for candidate ${candidateId}:`,
        error
      );
      return [];
    }
  }

  async createNote(
    candidateId: string,
    content: string,
    adminId: string = API_CONFIG.TEMP_ADMIN_ID
  ): Promise<ApiNote> {
    try {
      const { data, error } = await supabase
        .from("notes")
        .insert({
          candidate_id: candidateId,
          admin_id: adminId,
          content,
        })
        .select()
        .single();

      if (error) throw error;

      // Create timeline event
      await this.createTimelineEvent(
        candidateId,
        adminId,
        "Note Added",
        content.length > 50 ? content.substring(0, 50) + "..." : content,
        "note_added"
      );

      return data;
    } catch (error) {
      this.handleError(error, "Create note");
    }
  }

  async deleteNote(id: string): Promise<void> {
    try {
      const { error } = await supabase.from("notes").delete().eq("id", id);

      if (error) throw error;
    } catch (error) {
      this.handleError(error, "Delete note");
    }
  }

  // Timeline events methods
  async getTimelineEvents(candidateId: string): Promise<ApiTimelineEvent[]> {
    try {
      const { data, error } = await supabase
        .from("timeline_events")
        .select("*")
        .eq("candidate_id", candidateId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.warn(
        `Failed to fetch timeline events for candidate ${candidateId}:`,
        error
      );
      return [];
    }
  }

  async createTimelineEvent(
    candidateId: string,
    adminId: string = API_CONFIG.TEMP_ADMIN_ID,
    title: string,
    description: string,
    type: string
  ): Promise<ApiTimelineEvent> {
    try {
      const { data, error } = await supabase
        .from("timeline_events")
        .insert({
          candidate_id: candidateId,
          admin_id: adminId,
          title,
          description,
          type,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, "Create timeline event");
    }
  }
}

export const apiService = new ApiService();
