import { apiService } from "./api";
import { transformCandidate } from "../utils/transformers";
import { Candidate, ApplicationStatus } from "../types";
import { API_CONFIG } from "../config/constants";

/**
 * Service for managing candidate data and operations
 * Handles CRUD operations and business logic for candidates
 */
export class CandidateService {
  /**
   * Retrieves all candidates with their notes and timeline events
   * Uses optimized batch fetching to eliminate N+1 query pattern
   * @returns Array of candidates with complete details
   */
  async getAllCandidates(): Promise<Candidate[]> {
    const {
      candidates: apiCandidates,
      notes,
      timelineEvents,
    } = await apiService.getCandidatesWithDetails();

    if (apiCandidates.length === 0) {
      return [];
    }

    // Transform candidates with their associated notes and timeline events
    const candidatesWithDetails = apiCandidates.map((apiCandidate) => {
      const candidateNotes = notes[apiCandidate.id] || [];
      const candidateTimelineEvents = timelineEvents[apiCandidate.id] || [];

      return transformCandidate(
        apiCandidate,
        candidateNotes,
        candidateTimelineEvents
      );
    });

    return candidatesWithDetails;
  }

  /**
   * Retrieves a single candidate by ID with complete details
   * @param id - Candidate ID
   * @returns Candidate with details or null if not found
   */
  async getCandidateById(id: string): Promise<Candidate | null> {
    try {
      const [apiCandidate, notes, timelineEvents] = await Promise.all([
        apiService.getCandidate(id),
        apiService.getNotes(id),
        apiService.getTimelineEvents(id),
      ]);

      return transformCandidate(apiCandidate, notes, timelineEvents);
    } catch (error) {
      console.error("Get candidate error:", error);
      return null;
    }
  }

  /**
   * Updates candidate information
   * @param id - Candidate ID
   * @param updates - Partial candidate data to update
   * @returns Updated candidate with complete details
   */
  async updateCandidate(
    id: string,
    updates: Partial<Candidate>
  ): Promise<Candidate> {
    const apiUpdates = this.transformCandidateToApi(updates);
    const updatedApiCandidate = await apiService.updateCandidate(
      id,
      apiUpdates
    );

    // Get updated notes and timeline to return complete candidate data
    const [notes, timelineEvents] = await Promise.all([
      apiService.getNotes(id),
      apiService.getTimelineEvents(id),
    ]);

    return transformCandidate(updatedApiCandidate, notes, timelineEvents);
  }

  /**
   * Adds a note to a candidate
   * @param candidateId - ID of the candidate
   * @param content - Note content
   */
  async addNote(candidateId: string, content: string): Promise<void> {
    await apiService.createNote(candidateId, content, API_CONFIG.TEMP_ADMIN_ID);
  }

  /**
   * Updates candidate status and creates timeline event
   * @param id - Candidate ID
   * @param status - New application status
   */
  async updateStatus(id: string, status: ApplicationStatus): Promise<void> {
    await apiService.updateCandidate(id, { status });
    await apiService.createTimelineEvent(
      id,
      API_CONFIG.TEMP_ADMIN_ID,
      "Status Updated",
      `Status changed to ${status}`,
      "status_change"
    );
  }

  /**
   * Toggles the flagged status of a candidate
   * @param id - Candidate ID
   * @param currentFlag - Current flagged status
   */
  async toggleFlag(id: string, currentFlag: boolean): Promise<void> {
    await apiService.updateCandidate(id, { flagged: !currentFlag });
  }

  /**
   * Transforms internal candidate format to API format
   * @param candidate - Partial candidate data
   * @returns API-formatted candidate data
   */
  private transformCandidateToApi(candidate: Partial<Candidate>) {
    const apiCandidate: any = {};

    // Only include defined properties to avoid overwriting with undefined values
    if (candidate.firstName) apiCandidate.first_name = candidate.firstName;
    if (candidate.lastName) apiCandidate.last_name = candidate.lastName;
    if (candidate.email) apiCandidate.email = candidate.email;
    if (candidate.phone) apiCandidate.phone = candidate.phone;
    if (candidate.position) apiCandidate.position = candidate.position;
    if (candidate.storeLocation)
      apiCandidate.store_location = candidate.storeLocation;
    if (candidate.availability)
      apiCandidate.availability = candidate.availability;
    if (candidate.resume) apiCandidate.resume_url = candidate.resume;
    if (candidate.status) apiCandidate.status = candidate.status;
    if (candidate.flagged !== undefined)
      apiCandidate.flagged = candidate.flagged;

    return apiCandidate;
  }
}

// Export singleton instance for consistent usage across the app
export const candidateService = new CandidateService();
