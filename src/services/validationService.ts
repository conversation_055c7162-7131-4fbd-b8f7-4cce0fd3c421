import { Availability } from "../types";

/**
 * Result of a validation operation
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Structure for application form data
 */
export interface ApplicationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  storeLocation: string;
  availability: Availability[];
  experience: string;
  motivation: string;
  resumeFile: File | null;
}

/**
 * Service for validating form data and user inputs
 */
export class ValidationService {
  /**
   * Validates application form data
   * @param formData - Application form data to validate
   * @returns Validation result with errors if any
   */
  validateApplicationForm(formData: ApplicationFormData): ValidationResult {
    const errors: string[] = [];

    // Validate required text fields
    this.validateRequiredField(
      formData.firstName,
      "First name is required",
      errors
    );
    this.validateRequiredField(
      formData.lastName,
      "Last name is required",
      errors
    );
    this.validateRequiredField(
      formData.experience,
      "Experience description is required",
      errors
    );
    this.validateRequiredField(
      formData.motivation,
      "Motivation is required",
      errors
    );

    // Validate email
    if (!formData.email.trim()) {
      errors.push("Email is required");
    } else if (!this.isValidEmail(formData.email)) {
      errors.push("Please enter a valid email address");
    }

    // Validate phone
    this.validateRequiredField(
      formData.phone,
      "Phone number is required",
      errors
    );

    // Validate select fields
    if (!formData.position) {
      errors.push("Position is required");
    }

    if (!formData.storeLocation) {
      errors.push("Store location is required");
    }

    // Validate availability array
    if (formData.availability.length === 0) {
      errors.push("At least one availability option is required");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Helper method to validate required fields
   * @param value - Field value to validate
   * @param errorMessage - Error message to add if validation fails
   * @param errors - Array to push errors to
   */
  private validateRequiredField(
    value: string,
    errorMessage: string,
    errors: string[]
  ): void {
    if (!value.trim()) {
      errors.push(errorMessage);
    }
  }

  /**
   * Validates candidate update data
   * @param updates - Partial candidate data to validate
   * @returns Validation result with errors if any
   */
  validateCandidateUpdate(updates: any): ValidationResult {
    const errors: string[] = [];

    // Only validate fields that are being updated (not undefined)
    if (updates.firstName !== undefined && !updates.firstName.trim()) {
      errors.push("First name cannot be empty");
    }

    if (updates.lastName !== undefined && !updates.lastName.trim()) {
      errors.push("Last name cannot be empty");
    }

    if (updates.email !== undefined) {
      if (!updates.email.trim()) {
        errors.push("Email cannot be empty");
      } else if (!this.isValidEmail(updates.email)) {
        errors.push("Please enter a valid email address");
      }
    }

    if (
      updates.availability !== undefined &&
      updates.availability.length === 0
    ) {
      errors.push("At least one availability option is required");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validates email format using regex
   * @param email - Email string to validate
   * @returns True if email format is valid
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// Export singleton instance for consistent usage across the app
export const validationService = new ValidationService();
