import {
  Candidate,
  FilterOptions,
  Availability,
  ApplicationStatus,
} from "../types";

/**
 * Service for filtering candidate data based on various criteria
 */
export class FilterService {
  /**
   * Filters candidates based on provided filter options
   * @param candidates - Array of candidates to filter
   * @param filterOptions - Filter criteria to apply
   * @returns Filtered array of candidates
   */
  filterCandidates(
    candidates: Candidate[],
    filterOptions: FilterOptions
  ): Candidate[] {
    return candidates.filter((candidate) => {
      // Apply all filter criteria - candidate must pass all filters
      return (
        this.passesSearchFilter(candidate, filterOptions.search) &&
        this.passesPositionFilter(candidate, filterOptions.position) &&
        this.passesAvailabilityFilter(candidate, filterOptions.availability) &&
        this.passesLocationFilter(candidate, filterOptions.storeLocation) &&
        this.passesStatusFilter(candidate, filterOptions.status)
      );
    });
  }

  /**
   * Checks if candidate passes search filter
   */
  private passesSearchFilter(candidate: Candidate, search?: string): boolean {
    if (!search) return true;
    return this.matchesSearch(candidate, search);
  }

  /**
   * Checks if candidate passes position filter
   */
  private passesPositionFilter(
    candidate: Candidate,
    position?: string
  ): boolean {
    if (!position) return true;
    return candidate.position === position;
  }

  /**
   * Checks if candidate passes availability filter
   */
  private passesAvailabilityFilter(
    candidate: Candidate,
    availability: Availability[]
  ): boolean {
    if (availability.length === 0) return true;
    return this.matchesAvailability(candidate, availability);
  }

  /**
   * Checks if candidate passes location filter
   */
  private passesLocationFilter(
    candidate: Candidate,
    storeLocation?: string
  ): boolean {
    if (!storeLocation) return true;
    return candidate.storeLocation === storeLocation;
  }

  /**
   * Checks if candidate passes status filter
   */
  private passesStatusFilter(
    candidate: Candidate,
    status: ApplicationStatus[]
  ): boolean {
    if (status.length === 0) return true;
    return status.includes(candidate.status);
  }

  /**
   * Checks if candidate matches search term in name, position, or email
   * @param candidate - Candidate to check
   * @param searchTerm - Search term to match against
   * @returns True if candidate matches search term
   */
  private matchesSearch(candidate: Candidate, searchTerm: string): boolean {
    const searchValue = searchTerm.toLowerCase();
    const fullName =
      `${candidate.firstName} ${candidate.lastName}`.toLowerCase();

    return (
      fullName.includes(searchValue) ||
      candidate.position.toLowerCase().includes(searchValue) ||
      candidate.email.toLowerCase().includes(searchValue)
    );
  }

  /**
   * Checks if candidate's availability overlaps with filter availability
   * @param candidate - Candidate to check
   * @param filterAvailability - Availability options to match
   * @returns True if there's at least one matching availability
   */
  private matchesAvailability(
    candidate: Candidate,
    filterAvailability: Availability[]
  ): boolean {
    return candidate.availability.some((avail) =>
      filterAvailability.includes(avail)
    );
  }

  /**
   * Creates default filter options with empty values
   * @returns Default filter options object
   */
  createDefaultFilters(): FilterOptions {
    return {
      position: "",
      availability: [],
      storeLocation: "",
      status: [],
      search: "",
    };
  }
}

// Export singleton instance for consistent usage across the app
export const filterService = new FilterService();
