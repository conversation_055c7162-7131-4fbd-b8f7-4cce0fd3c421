/**
 * Service for formatting various data types consistently across the application
 */
export class FormatterService {
  /**
   * Formats a date string to a readable format (e.g., "Jan 15, 2024")
   * @param dateString - ISO date string to format
   * @returns Formatted date string
   */
  formatDate(dateString: string): string {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      }).format(date);
    } catch (error) {
      console.warn("Invalid date string provided to formatDate:", dateString);
      return dateString;
    }
  }

  /**
   * Formats a date string to include both date and time (e.g., "Jan 15, 2024, 2:30 PM")
   * @param dateString - ISO date string to format
   * @returns Formatted date and time string
   */
  formatDateTime(dateString: string): string {
    if (!dateString) return "";

    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      console.warn(
        "Invalid date string provided to formatDateTime:",
        dateString
      );
      return dateString;
    }
  }

  /**
   * Formats a phone number to US format (e.g., "(*************")
   * @param phone - Raw phone number string
   * @returns Formatted phone number or original string if invalid
   */
  formatPhoneNumber(phone: string): string {
    if (!phone) return "";

    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, "");

    // Format 10-digit US phone numbers
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
        6
      )}`;
    }

    // Return original if not a standard 10-digit number
    return phone;
  }

  /**
   * Truncates text to a specified length and adds ellipsis if needed
   * @param text - Text to truncate
   * @param maxLength - Maximum length before truncation
   * @returns Truncated text with ellipsis or original text if within limit
   */
  truncateText(text: string, maxLength: number): string {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  }

  /**
   * Returns appropriate CSS classes for application status badges
   * @param status - Application status string
   * @returns CSS class string for styling status badges
   */
  getStatusColor(status: string): string {
    const statusColorMap: Record<string, string> = {
      Hired: "bg-success bg-opacity-10 text-success",
      Declined: "bg-error bg-opacity-10 text-error",
      OnHold: "bg-warning bg-opacity-10 text-warning",
      Archived: "bg-neutral-200 text-neutral-700",
    };

    return statusColorMap[status] || "bg-gold bg-opacity-10 text-gold-dark";
  }
}

// Export singleton instance for consistent usage across the app
export const formatterService = new FormatterService();
