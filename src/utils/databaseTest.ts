import { supabase } from "../lib/supabase";

export const testDatabaseConnection = async () => {
  try {
    console.log("🔍 Testing database connection...");

    // Test basic connection with a simple query that doesn't require special permissions
    const { data: testData, error: testError } = await supabase
      .from("candidates")
      .select("id")
      .limit(1);

    if (testError) {
      console.error("❌ Database connection test failed:", testError);
      // Don't fail completely, just log the error
      console.log("⚠️ Database connection test failed, but continuing...");
      return false;
    }

    console.log("✅ Database connection successful");

    // Try to get count of candidates
    const { count, error: countError } = await supabase
      .from("candidates")
      .select("*", { count: "exact", head: true });

    if (countError) {
      console.error("❌ Error getting candidate count:", countError);
      console.log("📊 Unable to determine candidate count due to permissions");
    } else {
      console.log("📊 Current candidate count:", count);
    }

    // Check if we have any candidates (only if we can read them)
    const { data: candidates, error: candidatesError } = await supabase
      .from("candidates")
      .select("*")
      .limit(5);

    if (candidatesError) {
      console.error("❌ Error fetching candidates:", candidatesError);
      console.log("⚠️ Cannot fetch candidates, likely due to RLS policies");
      return false;
    }

    console.log("📋 Sample candidates:", candidates);

    // Only try to add sample data if we have proper permissions and no existing data
    if (count === 0 && candidates !== null) {
      console.log(
        "⚠️ No candidates found. Skipping sample data addition due to RLS policies..."
      );
      // Don't try to add sample data automatically as it requires proper authentication
    }

    return true;
  } catch (error) {
    console.error("💥 Database test failed:", error);
    console.log("⚠️ Database test failed, but application will continue...");
    return false;
  }
};

export const addSampleData = async () => {
  try {
    console.log("➕ Adding sample candidate data...");

    // Check if user is authenticated first
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      console.log("⚠️ Cannot add sample data: User not authenticated");
      console.log("💡 Please sign in to add sample data");
      return false;
    }

    const sampleCandidates = [
      {
        first_name: "Sarah",
        last_name: "Example",
        email: "<EMAIL>",
        phone: "(*************",
        position: "Massage Therapist",
        store_location: "Downtown",
        availability: ["Full-time", "Weekends"],
        status: "in_progress" as const,
        flagged: false,
      },
      {
        first_name: "Example",
        last_name: "Chen",
        email: "<EMAIL>",
        phone: "(*************",
        position: "Esthetician",
        store_location: "West Side",
        availability: ["Part-time", "Evenings"],
        status: "on_hold" as const,
        flagged: true,
      },
      {
        first_name: "Jane",
        last_name: "Doe",
        email: "<EMAIL>",
        phone: "(*************",
        position: "Spa Receptionist",
        store_location: "North End",
        availability: ["Full-time"],
        status: "hired" as const,
        flagged: false,
      },
    ];

    const { data, error } = await supabase
      .from("candidates")
      .insert(sampleCandidates)
      .select();

    if (error) {
      console.error("❌ Failed to add sample data:", error);
      console.log(
        "💡 This might be due to RLS policies. Make sure you are authenticated."
      );
      return false;
    }

    console.log(
      "✅ Sample data added successfully:",
      data?.length,
      "candidates"
    );
    return true;
  } catch (error) {
    console.error("💥 Failed to add sample data:", error);
    return false;
  }
};

// Only run the test in development and don't block the app
if (typeof window !== "undefined" && import.meta.env.DEV) {
  // Run test but don't block the application
  testDatabaseConnection().catch((error) => {
    console.log(
      "⚠️ Database test failed, but application will continue:",
      error
    );
  });
}
