import {
  ApiCandidate,
  ApiNote,
  ApiTimelineEvent,
  ApiAdmin,
} from "../types/api";
import { Candidate, Note, TimelineEvent, Admin } from "../types";

/**
 * Transforms API candidate data to internal candidate format
 * @param apiCandidate - Raw candidate data from API
 * @param notes - Array of notes associated with the candidate
 * @param timelineEvents - Array of timeline events for the candidate
 * @returns Transformed candidate object
 */
export const transformCandidate = (
  apiCandidate: ApiCandidate,
  notes: ApiNote[] = [],
  timelineEvents: ApiTimelineEvent[] = []
): Candidate => {
  return {
    id: apiCandidate.id,
    firstName: apiCandidate.first_name,
    lastName: apiCandidate.last_name,
    email: apiCandidate.email,
    phone: apiCandidate.phone,
    position: apiCandidate.position,
    storeLocation: apiCandidate.store_location,
    availability: apiCandidate.availability,
    resume: apiCandidate.resume_url,
    status: apiCandidate.status as any,
    experience: apiCandidate.experience,
    motivation: apiCandidate.motivation,
    createdAt: apiCandidate.created_at,
    updatedAt: apiCandidate.updated_at,
    flagged: apiCandidate.flagged,
    notes: notes.map(transformNote),
    timeline: timelineEvents.map(transformTimelineEvent),
  };
};

/**
 * Transforms API note data to internal note format
 * @param apiNote - Raw note data from API
 * @returns Transformed note object
 */
export const transformNote = (apiNote: ApiNote): Note => {
  return {
    id: apiNote.id,
    candidateId: apiNote.candidate_id,
    text: apiNote.content,
    createdAt: apiNote.created_at,
    createdBy: "Admin", // Default value - could be enhanced to use actual admin data
  };
};

/**
 * Transforms API timeline event data to internal timeline event format
 * @param apiEvent - Raw timeline event data from API
 * @returns Transformed timeline event object
 */
export const transformTimelineEvent = (
  apiEvent: ApiTimelineEvent
): TimelineEvent => {
  return {
    id: apiEvent.id,
    candidateId: apiEvent.candidate_id,
    title: apiEvent.title,
    description: apiEvent.description,
    date: apiEvent.created_at,
    type: apiEvent.type as any,
  };
};

/**
 * Transforms API admin data to internal admin format
 * @param apiAdmin - Raw admin data from API
 * @returns Transformed admin object
 */
export const transformAdmin = (apiAdmin: ApiAdmin): Admin => {
  return {
    id: apiAdmin.id,
    username: apiAdmin.username,
    name: apiAdmin.name,
    jobTitle: apiAdmin.job_title,
    email: apiAdmin.email,
    phone: apiAdmin.phone,
  };
};
/**
 * Transforms internal admin data to API format for updates
 * @param admin - Partial admin data to transform
 * @returns Transformed API admin object
 */
export const transformAdminToApi = (
  admin: Partial<Admin>
): Partial<ApiAdmin> => {
  const apiAdmin: Partial<ApiAdmin> = {};

  // Only include defined properties to avoid overwriting with undefined values
  if (admin.id !== undefined) apiAdmin.id = admin.id;
  if (admin.username !== undefined) apiAdmin.username = admin.username;
  if (admin.name !== undefined) apiAdmin.name = admin.name;
  if (admin.jobTitle !== undefined) apiAdmin.job_title = admin.jobTitle;
  if (admin.email !== undefined) apiAdmin.email = admin.email;
  if (admin.phone !== undefined) apiAdmin.phone = admin.phone;

  return apiAdmin;
};
