export interface ApiCandidate {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  position: string;
  store_location: string;
  availability: string[];
  resume_url?: string;
  status: string;
  flagged: boolean;
  experience: string;
  motivation: string;
  created_at: string;
  updated_at: string;
}

export interface ApiNote {
  id: string;
  candidate_id: string;
  admin_id: string;
  content: string;
  created_at: string;
}

export interface ApiTimelineEvent {
  id: string;
  candidate_id: string;
  admin_id: string;
  title: string;
  description: string;
  type: string;
  created_at: string;
}

export interface ApiAdmin {
  id: string;
  username: string;
  name: string;
  job_title: string;
  email: string;
  phone: string;
  created_at: string;
  updated_at: string;
}

export interface ApiError {
  message: string;
  details?: string;
  hint?: string;
}