export interface Admin {
  id: string;
  username: string;
  name: string;
  jobTitle: string;
  email: string;
  phone: string;
  avatar?: string;
}

export type ApplicationStatus = 'in_progress' | 'on_hold' | 'declined' | 'hired' | 'archived' | 'new';

export type Availability = 'Full-time' | 'Part-time' | 'Weekends' | 'Evenings' | 'Flexible';

export interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  storeLocation: string;
  availability: Availability[];
  resume?: string;
  status: ApplicationStatus;
  experience: string;
  motivation: string;
  createdAt: string;
  updatedAt: string;
  notes: Note[];
  timeline: TimelineEvent[];
  flagged: boolean;
}

export interface Note {
  id: string;
  candidateId: string;
  text: string;
  createdAt: string;
  createdBy: string;
}

export interface TimelineEvent {
  id: string;
  candidateId: string;
  title: string;
  description: string;
  date: string;
  type: 'status_change' | 'note_added' | 'interview_scheduled' | 'other';
}

export interface FilterOptions {
  position: string;
  availability: Availability[];
  storeLocation: string;
  status: ApplicationStatus[];
  search: string;
}

export interface DashboardMetrics {
  activeApplications: number;
  hires: number;
  archived: number;
}

// Error handling types
export interface ErrorState {
  message: string;
  details?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  error: string | null;
  success: boolean;
}

// Async operation state
export interface AsyncOperationState {
  loading: boolean;
  error: string | null;
  data: any;
}