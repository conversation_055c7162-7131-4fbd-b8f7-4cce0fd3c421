import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types based on your existing schema
export type Database = {
  public: {
    Tables: {
      admins: {
        Row: {
          id: string;
          username: string;
          name: string;
          job_title: string;
          email: string;
          phone: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          username: string;
          name: string;
          job_title?: string;
          email: string;
          phone?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          username?: string;
          name?: string;
          job_title?: string;
          email?: string;
          phone?: string;
          updated_at?: string;
        };
      };
      candidates: {
        Row: {
          id: string;
          first_name: string;
          last_name: string;
          email: string;
          phone: string;
          position: string;
          store_location: string;
          availability: string[];
          resume_url: string;
          flagged: boolean;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          first_name: string;
          last_name: string;
          email: string;
          phone: string;
          position: string;
          store_location: string;
          availability?: string[];
          resume_url?: string;
          flagged?: boolean;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          first_name?: string;
          last_name?: string;
          email?: string;
          phone?: string;
          position?: string;
          store_location?: string;
          availability?: string[];
          resume_url?: string;
          flagged?: boolean;
          status?: string;
          updated_at?: string;
        };
      };
      notes: {
        Row: {
          id: string;
          candidate_id: string;
          admin_id: string;
          content: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          candidate_id: string;
          admin_id: string;
          content: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          candidate_id?: string;
          admin_id?: string;
          content?: string;
        };
      };
      timeline_events: {
        Row: {
          id: string;
          candidate_id: string;
          admin_id: string;
          title: string;
          description: string;
          type: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          candidate_id: string;
          admin_id: string;
          title: string;
          description?: string;
          type: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          candidate_id?: string;
          admin_id?: string;
          title?: string;
          description?: string;
          type?: string;
        };
      };
    };
  };
};