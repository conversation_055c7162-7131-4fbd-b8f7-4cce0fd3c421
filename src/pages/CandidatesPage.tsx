import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import AppLayout from "../components/layout/AppLayout";
import CandidateCard from "../components/candidates/CandidateCard";
import CandidateFilter from "../components/candidates/CandidateFilter";
import useCandidateStore from "../stores/candidateStore";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import ErrorMessage from "../components/ui/ErrorMessage";

/**
 * Candidates page component displaying filterable list of candidates
 * Features filtering, search, and candidate management functionality
 */
const CandidatesPage = () => {
  const {
    filteredCandidates,
    filterOptions,
    loading,
    error,
    fetchCandidates,
    updateCandidate,
    toggleFlag,
    setFilterOptions,
    resetFilters,
    clearError,
  } = useCandidateStore();

  const location = useLocation();

  /**
   * Fetches candidates on component mount
   */
  useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  /**
   * Applies filter from dashboard navigation if present
   */
  useEffect(() => {
    // Apply filter if coming from dashboard metrics
    if (location.state?.filterStatus) {
      setFilterOptions({
        status: [location.state.filterStatus],
      });
    }
  }, [location.state?.filterStatus, setFilterOptions]);

  if (loading) {
    return (
      <AppLayout title="Candidates">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title="Candidates">
      {error && (
        <ErrorMessage message={error} onDismiss={clearError} className="mb-6" />
      )}

      <CandidateFilter
        filterOptions={filterOptions}
        onFilterChange={setFilterOptions}
        onReset={resetFilters}
      />

      {filteredCandidates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <CandidateCard
              key={candidate.id}
              candidate={candidate}
              onUpdate={updateCandidate}
              onToggleFlag={toggleFlag}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <p className="text-neutral-600">
            No candidates found matching your filters.
          </p>
        </div>
      )}
    </AppLayout>
  );
};

export default CandidatesPage;
