import { useState } from 'react';
import { Building2, Send, Upload, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { apiService } from '../services/api';
import { validationService, ApplicationFormData } from '../services/validationService';
import { useAsyncOperation } from '../hooks/useAsyncOperation';
import { POSITIONS, STORE_LOCATIONS, AVAILABILITY_OPTIONS } from '../config/constants';
import { Availability } from '../types';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import ErrorMessage from '../components/ui/ErrorMessage';

const ApplicationForm = () => {
  const [formData, setFormData] = useState<ApplicationFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    storeLocation: '',
    availability: [],
    experience: '',
    motivation: '',
    resumeFile: null
  });
  
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { loading, error, execute, clearError } = useAsyncOperation();
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setValidationErrors([]);
  };
  
  const handleAvailabilityChange = (availability: Availability) => {
    setFormData(prev => {
      const currentAvailability = [...prev.availability];
      
      if (currentAvailability.includes(availability)) {
        return {
          ...prev,
          availability: currentAvailability.filter(a => a !== availability)
        };
      } else {
        return {
          ...prev,
          availability: [...currentAvailability, availability]
        };
      }
    });
    setValidationErrors([]);
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, resumeFile: file }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validationService.validateApplicationForm(formData);
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }
    
    const result = await execute(async () => {
      const candidateData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        position: formData.position,
        store_location: formData.storeLocation,
        availability: formData.availability,
        resume_url: formData.resumeFile ? `https://example.com/resumes/${formData.resumeFile.name}` : '',
        flagged: false,
        status: 'in_progress'
      };
      
      const newCandidate = await apiService.createCandidate(candidateData);
      
      // Create timeline event for application submission
      await apiService.createTimelineEvent(
        newCandidate.id,
        'temp-admin-id',
        'Application Submitted',
        `${formData.firstName} ${formData.lastName} submitted application for ${formData.position}`,
        'status_change'
      );
      
      return newCandidate;
    }, 'Failed to submit application');
    
    if (result) {
      setIsSubmitted(true);
    }
  };
  
  if (isSubmitted) {
    return <ApplicationSuccessScreen />;
  }
  
  return (
    <div className="min-h-screen bg-neutral-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <ApplicationHeader />
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-lg shadow-lg p-8"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <ErrorMessage 
                message={error}
                onDismiss={clearError}
              />
            )}
            
            {validationErrors.length > 0 && (
              <ErrorMessage 
                message={`Please fix the following errors: ${validationErrors.join(', ')}`}
                onDismiss={() => setValidationErrors([])}
              />
            )}
            
            <PersonalInformationSection 
              formData={formData}
              onChange={handleInputChange}
            />
            
            <PositionInformationSection 
              formData={formData}
              onChange={handleInputChange}
            />
            
            <AvailabilitySection 
              availability={formData.availability}
              onChange={handleAvailabilityChange}
            />
            
            <ExperienceSection 
              formData={formData}
              onChange={handleInputChange}
            />
            
            <ResumeUploadSection 
              resumeFile={formData.resumeFile}
              onChange={handleFileChange}
            />
            
            <div className="pt-6">
              <Button
                type="submit"
                loading={loading}
                icon={!loading ? <Send size={20} /> : undefined}
                className="w-full"
              >
                {loading ? 'Submitting Application...' : 'Submit Application'}
              </Button>
            </div>
            
            <p className="text-xs text-neutral-500 text-center">
              By submitting this application, you agree to our terms and conditions and privacy policy.
            </p>
          </form>
        </motion.div>
      </div>
    </div>
  );
};

const ApplicationHeader = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="text-center mb-8"
  >
    <Building2 className="mx-auto h-12 w-12 text-gold mb-4" />
    <h1 className="text-3xl font-bold text-black mb-2">
      Join Our Medical Spa Team
    </h1>
    <p className="text-neutral-600">
      We're looking for passionate professionals to join our growing team. 
      Fill out the application below to get started.
    </p>
  </motion.div>
);

const PersonalInformationSection = ({ formData, onChange }: {
  formData: ApplicationFormData;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => (
  <div>
    <h3 className="text-lg font-semibold text-black mb-4">Personal Information</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Input
        label="First Name *"
        name="firstName"
        value={formData.firstName}
        onChange={onChange}
        required
      />
      
      <Input
        label="Last Name *"
        name="lastName"
        value={formData.lastName}
        onChange={onChange}
        required
      />
      
      <Input
        label="Email Address *"
        type="email"
        name="email"
        value={formData.email}
        onChange={onChange}
        required
      />
      
      <Input
        label="Phone Number *"
        type="tel"
        name="phone"
        value={formData.phone}
        onChange={onChange}
        placeholder="(*************"
        required
      />
    </div>
  </div>
);

const PositionInformationSection = ({ formData, onChange }: {
  formData: ApplicationFormData;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
}) => (
  <div>
    <h3 className="text-lg font-semibold text-black mb-4">Position Information</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-1">
          Position Applying For *
        </label>
        <select
          name="position"
          required
          className="input w-full"
          value={formData.position}
          onChange={onChange}
        >
          <option value="">Select a position</option>
          {POSITIONS.map((position) => (
            <option key={position} value={position}>
              {position}
            </option>
          ))}
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-neutral-700 mb-1">
          Preferred Store Location *
        </label>
        <select
          name="storeLocation"
          required
          className="input w-full"
          value={formData.storeLocation}
          onChange={onChange}
        >
          <option value="">Select a location</option>
          {STORE_LOCATIONS.map((location) => (
            <option key={location} value={location}>
              {location}
            </option>
          ))}
        </select>
      </div>
    </div>
  </div>
);

const AvailabilitySection = ({ availability, onChange }: {
  availability: Availability[];
  onChange: (availability: Availability) => void;
}) => (
  <div>
    <h3 className="text-lg font-semibold text-black mb-4">Availability *</h3>
    <p className="text-sm text-neutral-600 mb-3">Select all that apply:</p>
    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
      {AVAILABILITY_OPTIONS.map((option) => (
        <label key={option} className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            className="form-checkbox rounded text-gold focus:ring-gold"
            checked={availability.includes(option)}
            onChange={() => onChange(option)}
          />
          <span className="text-sm text-neutral-700">{option}</span>
        </label>
      ))}
    </div>
  </div>
);

const ExperienceSection = ({ formData, onChange }: {
  formData: ApplicationFormData;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}) => (
  <>
    <div>
      <label className="block text-sm font-medium text-neutral-700 mb-1">
        Relevant Experience *
      </label>
      <textarea
        name="experience"
        required
        rows={4}
        className="input w-full resize-none"
        placeholder="Tell us about your relevant experience in the spa/wellness industry..."
        value={formData.experience}
        onChange={onChange}
      />
    </div>
    
    <div>
      <label className="block text-sm font-medium text-neutral-700 mb-1">
        Why do you want to work with us? *
      </label>
      <textarea
        name="motivation"
        required
        rows={3}
        className="input w-full resize-none"
        placeholder="What motivates you to join our team?"
        value={formData.motivation}
        onChange={onChange}
      />
    </div>
  </>
);

const ResumeUploadSection = ({ resumeFile, onChange }: {
  resumeFile: File | null;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => (
  <div>
    <label className="block text-sm font-medium text-neutral-700 mb-1">
      Resume (Optional)
    </label>
    <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-neutral-300 border-dashed rounded-md hover:border-gold transition-colors">
      <div className="space-y-1 text-center">
        <Upload className="mx-auto h-12 w-12 text-neutral-400" />
        <div className="flex text-sm text-neutral-600">
          <label
            htmlFor="resume"
            className="relative cursor-pointer bg-white rounded-md font-medium text-gold hover:text-gold-dark focus-within:outline-none"
          >
            <span>Upload a file</span>
            <input
              id="resume"
              name="resume"
              type="file"
              className="sr-only"
              accept=".pdf,.doc,.docx"
              onChange={onChange}
            />
          </label>
          <p className="pl-1">or drag and drop</p>
        </div>
        <p className="text-xs text-neutral-500">PDF, DOC, DOCX up to 10MB</p>
        {resumeFile && (
          <p className="text-sm text-gold font-medium">
            Selected: {resumeFile.name}
          </p>
        )}
      </div>
    </div>
  </div>
);

const ApplicationSuccessScreen = () => (
  <div className="min-h-screen bg-neutral-50 flex items-center justify-center py-12 px-4">
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-md w-full text-center"
    >
      <div className="bg-white rounded-lg shadow-lg p-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="w-16 h-16 bg-success rounded-full flex items-center justify-center mx-auto mb-6"
        >
          <CheckCircle size={32} className="text-white" />
        </motion.div>
        
        <h2 className="text-2xl font-bold text-black mb-4">
          Application Submitted!
        </h2>
        
        <p className="text-neutral-600 mb-6">
          Thank you for your interest in joining our team. We've received your application and will review it shortly. You'll hear from us within 3-5 business days.
        </p>
        
        <div className="bg-neutral-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-black mb-2">What's Next?</h3>
          <ul className="text-sm text-neutral-600 space-y-1">
            <li>• We'll review your application and resume</li>
            <li>• If selected, we'll contact you for an interview</li>
            <li>• Check your email for updates</li>
          </ul>
        </div>
        
        <Button
          onClick={() => window.location.reload()}
          className="w-full"
        >
          Submit Another Application
        </Button>
      </div>
    </motion.div>
  </div>
);

export default ApplicationForm;