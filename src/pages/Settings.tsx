import { useState } from 'react';
import { User, Mail, Phone, AtSign, Lock, Save } from 'lucide-react';
import AppLayout from '../components/layout/AppLayout';
import useAuthStore from '../stores/authStore';
import { useAsyncOperation } from '../hooks/useAsyncOperation';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import ErrorMessage from '../components/ui/ErrorMessage';

const Settings = () => {
  const { admin, updateAdmin } = useAuthStore();
  const { loading, error, execute, clearError } = useAsyncOperation();
  
  const [formData, setFormData] = useState({
    name: admin?.name || '',
    jobTitle: admin?.jobTitle || '',
    email: admin?.email || '',
    phone: admin?.phone || '',
    username: admin?.username || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [success, setSuccess] = useState(false);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setSuccess(false);
    clearError();
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = await execute(async () => {
      if (formData.newPassword) {
        if (formData.newPassword !== formData.confirmPassword) {
          throw new Error('New passwords do not match');
        }
        
        if (!formData.currentPassword) {
          throw new Error('Current password is required to change password');
        }
      }
      
      const updateData = {
        name: formData.name,
        jobTitle: formData.jobTitle,
        email: formData.email,
        phone: formData.phone,
        username: formData.username
      };
      
      await updateAdmin(updateData);
      
      setFormData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }));
      
      return true;
    }, 'Failed to update profile');
    
    if (result) {
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    }
  };
  
  return (
    <AppLayout title="Settings">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          {success && (
            <div className="mb-6 p-3 bg-success/10 text-success rounded-md">
              Profile updated successfully!
            </div>
          )}
          
          {error && (
            <ErrorMessage 
              message={error}
              onDismiss={clearError}
              className="mb-6"
            />
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                icon={<User size={18} />}
                required
              />
              
              <Input
                label="Job Title"
                name="jobTitle"
                value={formData.jobTitle}
                onChange={handleChange}
                required
              />
              
              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                icon={<Mail size={18} />}
                required
              />
              
              <Input
                label="Phone Number"
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                icon={<Phone size={18} />}
                required
              />
            </div>
            
            <div className="border-t border-neutral-200 pt-6">
              <h3 className="text-lg font-medium text-neutral-900 mb-4">
                Account Settings
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  icon={<AtSign size={18} />}
                  required
                />
                
                <div className="md:col-span-2">
                  <div className="bg-neutral-50 rounded-md p-4">
                    <h4 className="text-sm font-medium text-neutral-900 mb-2">
                      Change Password
                    </h4>
                    <p className="text-sm text-neutral-500 mb-4">
                      Leave these fields empty if you don't want to change your password.
                    </p>
                    
                    <div className="space-y-4">
                      <Input
                        label="Current Password"
                        type="password"
                        name="currentPassword"
                        value={formData.currentPassword}
                        onChange={handleChange}
                        icon={<Lock size={18} />}
                      />
                      
                      <Input
                        label="New Password"
                        type="password"
                        name="newPassword"
                        value={formData.newPassword}
                        onChange={handleChange}
                        icon={<Lock size={18} />}
                      />
                      
                      <Input
                        label="Confirm New Password"
                        type="password"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        icon={<Lock size={18} />}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end">
              <Button
                type="submit"
                loading={loading}
                icon={<Save size={18} />}
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  );
};

export default Settings;