import { useEffect, useMemo, useCallback } from "react";
import { Users, UserCheck, Archive } from "lucide-react";
import AppLayout from "../components/layout/AppLayout";
import MetricCard from "../components/dashboard/MetricCard";
import RecentCandidatesList from "../components/dashboard/RecentCandidatesList";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import useCandidateStore from "../stores/candidateStore";

/**
 * Dashboard page component displaying key metrics and recent candidates
 * Shows active applications, hires, archived candidates, and recent candidate list
 */
const DashboardPage = () => {
  const {
    candidates,
    loading,
    error,
    fetchCandidates,
    clearError,
    toggleFlag,
  } = useCandidateStore();

  console.log(
    "📊 Dashboard render - candidates:",
    candidates.length,
    "loading:",
    loading,
    "error:",
    error
  );

  /**
   * Fetches candidates on component mount
   */
  useEffect(() => {
    console.log("🎯 Dashboard useEffect - fetching candidates");
    fetchCandidates();
  }, [fetchCandidates]);

  /**
   * Calculates metrics from candidate data
   * Memoized to prevent recalculation on every render
   */
  const { activeApplications, hires, archived } = useMemo(() => {
    const activeApplications = candidates.filter(
      (c) => c.status === "in_progress" || c.status === "on_hold"
    ).length;

    const hires = candidates.filter((c) => c.status === "hired").length;
    const archived = candidates.filter((c) => c.status === "archived").length;

    return { activeApplications, hires, archived };
  }, [candidates]);

  /**
   * Handles retry action for failed data loading
   */
  const handleRetry = useCallback(() => {
    clearError();
    fetchCandidates();
  }, [clearError, fetchCandidates]);

  if (loading) {
    return (
      <AppLayout title="Dashboard">
        <div className="text-center mb-4">
          <p className="text-neutral-600">
            Loading candidates from database...
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title="Dashboard">
      {error && (
        <div className="mb-6">
          <div className="bg-error/10 border border-error/20 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-error">
                  Database Error
                </h3>
                <div className="mt-2 text-sm text-error/80">{error}</div>
                <div className="mt-3">
                  <button
                    onClick={handleRetry}
                    className="text-sm bg-error text-white px-3 py-1 rounded hover:bg-error/80"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <MetricCard
          title="Active Applications"
          value={activeApplications}
          icon={<Users size={24} />}
          status="In Progress"
        />

        <MetricCard
          title="Hires"
          value={hires}
          icon={<UserCheck size={24} />}
          color="bg-success"
          status="Hired"
        />

        <MetricCard
          title="Archived"
          value={archived}
          icon={<Archive size={24} />}
          color="bg-neutral-200"
          status="Archived"
        />
      </div>

      <div className="mt-8">
        <RecentCandidatesList
          candidates={candidates}
          onToggleFlag={toggleFlag}
        />
      </div>
    </AppLayout>
  );
};

export default DashboardPage;
