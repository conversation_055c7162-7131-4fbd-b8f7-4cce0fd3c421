import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Send
} from 'lucide-react';
import { motion } from 'framer-motion';
import AppLayout from '../components/layout/AppLayout';
import useCandidateStore from '../stores/candidateStore';
import { Candidate, ApplicationStatus } from '../types';
import LoadingSpinner from '../components/ui/LoadingSpinner';

const statusOptions: { value: ApplicationStatus; label: string; color: string }[] = [
  { value: 'in_progress', label: 'In Progress', color: 'bg-gold' },
  { value: 'on_hold', label: 'On Hold', color: 'bg-warning' },
  { value: 'declined', label: 'Declined', color: 'bg-error' },
  { value: 'hired', label: 'Hired', color: 'bg-success' },
  { value: 'archived', label: 'Archived', color: 'bg-neutral-200' }
];

const CandidateDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getCandidate, updateCandidate, addNote, toggleFlag, updateStatus } = useCandidateStore();
  
  const [candidate, setCandidate] = useState<Candidate | null>(null);
  const [loading, setLoading] = useState(true);
  const [newNote, setNewNote] = useState('');
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchCandidate = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const candidateData = await getCandidate(id);
        if (candidateData) {
          setCandidate(candidateData);
        } else {
          setError('Candidate not found');
        }
      } catch (err) {
        setError('Failed to load candidate');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCandidate();
  }, [id, getCandidate]);
  
  const handleStatusChange = async (status: ApplicationStatus) => {
    if (!candidate) return;
    
    try {
      await updateStatus(candidate.id, status);
      setCandidate(prev => prev ? { ...prev, status } : null);
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };
  
  const handleToggleFlag = async () => {
    if (!candidate) return;
    
    try {
      await toggleFlag(candidate.id);
      setCandidate(prev => prev ? { ...prev, flagged: !prev.flagged } : null);
    } catch (error) {
      console.error('Failed to toggle flag:', error);
    }
  };
  
  const handleAddNote = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!candidate || !newNote.trim()) return;
    
    try {
      await addNote(candidate.id, newNote);
      setNewNote('');
      // Refresh candidate data
      const updatedCandidate = await getCandidate(candidate.id);
      if (updatedCandidate) {
        setCandidate(updatedCandidate);
      }
    } catch (error) {
      console.error('Failed to add note:', error);
    }
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  if (loading) {
    return (
      <AppLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AppLayout>
    );
  }
  
  if (error || !candidate) {
    return (
      <AppLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-black mb-4">
            {error || 'Candidate Not Found'}
          </h2>
          <p className="text-neutral-600 mb-6">
            The candidate you're looking for doesn't exist or couldn't be loaded.
          </p>
          <button
            onClick={() => navigate('/candidates')}
            className="btn btn-primary"
          >
            Back to Candidates
          </button>
        </div>
      </AppLayout>
    );
  }
  
  return (
    <AppLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <button
          onClick={() => navigate('/candidates')}
          className="mb-6 flex items-center text-neutral-600 hover:text-black transition-colors duration-200"
        >
          <ArrowLeft size={20} className="mr-2" />
          Back to Candidates
        </button>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Candidate Header */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-black">
                    {candidate.firstName} {candidate.lastName}
                  </h1>
                  <p className="text-lg text-neutral-600 mt-1">{candidate.position}</p>
                </div>
                
                <div className="flex items-center space-x-3">
                  <button
                    onClick={handleToggleFlag}
                    className={`p-2 rounded-full transition-colors duration-200 ${
                      candidate.flagged 
                        ? 'bg-gold text-white' 
                        : 'bg-neutral-100 text-neutral-400 hover:bg-gold hover:text-white'
                    }`}
                  >
                    {candidate.flagged ? <ThumbsUp size={20} /> : <ThumbsDown size={20} />}
                  </button>
                  
                  <select
                    value={candidate.status}
                    onChange={(e) => handleStatusChange(e.target.value as ApplicationStatus)}
                    className="input py-2 px-3 text-sm"
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center">
                  <Mail size={20} className="text-neutral-400 mr-3" />
                  <div>
                    <p className="text-sm text-neutral-500">Email</p>
                    <p className="font-medium">{candidate.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Phone size={20} className="text-neutral-400 mr-3" />
                  <div>
                    <p className="text-sm text-neutral-500">Phone</p>
                    <p className="font-medium">{candidate.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <MapPin size={20} className="text-neutral-400 mr-3" />
                  <div>
                    <p className="text-sm text-neutral-500">Store Location</p>
                    <p className="font-medium">{candidate.storeLocation}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Calendar size={20} className="text-neutral-400 mr-3" />
                  <div>
                    <p className="text-sm text-neutral-500">Availability</p>
                    <p className="font-medium">{candidate.availability.join(', ')}</p>
                  </div>
                </div>
                
                {candidate.resume && (
                  <div className="flex items-center md:col-span-2">
                    <FileText size={20} className="text-neutral-400 mr-3" />
                    <div>
                      <p className="text-sm text-neutral-500">Resume</p>
                      <a
                        href={candidate.resume}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-medium text-gold hover:text-gold-dark flex items-center"
                      >
                        View Resume
                        <ExternalLink size={16} className="ml-1" />
                      </a>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Application Details */}
              {candidate.experience && candidate.motivation && (
                <div className="mt-6 pt-6 border-t border-neutral-200">
                  <h4 className="font-semibold text-black mb-3">Application Details</h4>
                  
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-neutral-700 mb-1">Experience</p>
                      <p className="text-sm text-neutral-600 bg-neutral-50 p-3 rounded-md">
                        {candidate.experience}
                      </p>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-neutral-700 mb-1">Motivation</p>
                      <p className="text-sm text-neutral-600 bg-neutral-50 p-3 rounded-md">
                        {candidate.motivation}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Timeline */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-black mb-4">Timeline</h3>
              
              {candidate.timeline.length > 0 ? (
                <div className="space-y-4">
                  {candidate.timeline.map((event) => (
                    <div key={event.id} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-gold rounded-full mt-2"></div>
                      <div>
                        <p className="font-medium text-black">{event.title}</p>
                        <p className="text-sm text-neutral-600">{event.description}</p>
                        <p className="text-xs text-neutral-400 mt-1">
                          {formatDate(event.date)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-neutral-500">No timeline events yet.</p>
              )}
            </div>
          </div>
          
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-black mb-4">Notes</h3>
              
              {/* Add Note Form */}
              <form onSubmit={handleAddNote} className="mb-6">
                <textarea
                  className="input w-full resize-none"
                  placeholder="Add a note..."
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  rows={3}
                />
                <button
                  type="submit"
                  disabled={!newNote.trim()}
                  className="btn btn-primary w-full mt-2 flex items-center justify-center"
                >
                  <Send size={16} className="mr-2" />
                  Add Note
                </button>
              </form>
              
              {/* Notes List */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {candidate.notes.length > 0 ? (
                  candidate.notes.map((note) => (
                    <div key={note.id} className="bg-neutral-50 rounded-lg p-3">
                      <p className="text-sm text-black">{note.text}</p>
                      <div className="flex justify-between items-center mt-2">
                        <p className="text-xs text-neutral-500">
                          {formatDate(note.createdAt)} by {note.createdBy}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-neutral-500 text-center py-4">No notes yet.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AppLayout>
  );
};

export default CandidateDetails;