import { Link } from 'react-router-dom';
import { Home } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-light py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#bc9a64" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mx-auto mb-6">
          <path d="M18 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2Z"></path>
          <path d="M9 22V18H15V22"></path>
          <path d="M15 2V6H9V2"></path>
          <path d="M10 6V18"></path>
          <path d="M14 6V18"></path>
        </svg>
        
        <h1 className="text-6xl font-serif font-bold text-black">404</h1>
        <h2 className="text-2xl font-serif font-bold text-black mt-2">Page Not Found</h2>
        <p className="mt-4 text-gray-dark">
          The page you are looking for doesn't exist or has been moved.
        </p>
        
        <div className="mt-8">
          <Link to="/dashboard" className="btn btn-primary inline-flex items-center">
            <Home size={18} className="mr-2" />
            Back to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}