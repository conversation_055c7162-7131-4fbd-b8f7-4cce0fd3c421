import { create } from "zustand";
import { apiService } from "../services/api";
import { transformAdmin, transformAdminToApi } from "../utils/transformers";
import { Admin } from "../types";
import { supabase } from "../lib/supabase";

/**
 * Authentication store state interface
 */
interface AuthState {
  isAuthenticated: boolean;
  admin: Admin | null;
  loading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  updateAdmin: (updates: Partial<Admin>) => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

const useAuthStore = create<AuthState>((set, get) => ({
  isAuthenticated: false, // Start as unauthenticated
  admin: null,
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  /**
   * Checks current authentication status and loads admin profile
   */
  checkAuth: async () => {
    try {
      set({ loading: true, error: null });

      const {
        data: { session },
      } = await apiService.getSession();

      if (session?.user) {
        const apiAdmin = await apiService.getAdmin(session.user.id);

        if (apiAdmin) {
          // Admin profile exists
          const admin = transformAdmin(apiAdmin);
          set({
            isAuthenticated: true,
            admin,
            loading: false,
          });
        } else {
          // Admin profile doesn't exist, create one
          try {
            const newAdmin = {
              id: session.user.id,
              username: session.user.email?.split("@")[0] || "admin",
              name: session.user.user_metadata?.full_name || "Admin User",
              job_title: "Administrator",
              email: session.user.email || "",
              phone: session.user.user_metadata?.phone || "",
            };

            const createdAdmin = await apiService.createAdmin(newAdmin);
            const admin = transformAdmin(createdAdmin);

            set({
              isAuthenticated: true,
              admin,
              loading: false,
            });
          } catch (createError) {
            console.error("Failed to create admin profile:", createError);
            set({
              isAuthenticated: false,
              admin: null,
              loading: false,
              error: "Failed to create admin profile",
            });
          }
        }
      } else {
        set({
          isAuthenticated: false,
          admin: null,
          loading: false,
        });
      }
    } catch (error: any) {
      console.error("Auth check error:", error);
      set({
        isAuthenticated: false,
        admin: null,
        loading: false,
        error: error.message || "Authentication check failed",
      });
    }
  },

  /**
   * Authenticates user with email and password
   * @param email - User's email address
   * @param password - User's password
   * @returns True if login successful, false otherwise
   */
  login: async (email: string, password: string) => {
    try {
      set({ loading: true, error: null });

      const { data, error } = await apiService.signIn(email, password);

      if (error || !data.session) {
        set({
          loading: false,
          error: "Login failed. Please check your credentials and try again.",
        });
        return false;
      }

      const apiAdmin = await apiService.getAdmin(data.session.user.id);

      if (apiAdmin) {
        // Admin profile exists
        const admin = transformAdmin(apiAdmin);
        set({
          isAuthenticated: true,
          admin,
          loading: false,
        });
        return true;
      } else {
        // Admin profile doesn't exist, create one
        try {
          const newAdmin = {
            id: data.session.user.id,
            username: data.session.user.email?.split("@")[0] || "admin",
            name: data.session.user.user_metadata?.full_name || "Admin User",
            job_title: "Administrator",
            email: data.session.user.email || "",
            phone: data.session.user.user_metadata?.phone || "",
          };

          const createdAdmin = await apiService.createAdmin(newAdmin);
          const admin = transformAdmin(createdAdmin);

          set({
            isAuthenticated: true,
            admin,
            loading: false,
          });
          return true;
        } catch (createError) {
          console.error("Failed to create admin profile:", createError);
          set({
            loading: false,
            error: "Failed to create admin profile",
          });
          return false;
        }
      }
    } catch (error: any) {
      console.error("Login error:", error);
      set({
        loading: false,
        error:
          error.message ||
          "Login failed. Please check your credentials and try again.",
      });
      return false;
    }
  },

  /**
   * Signs out the current user and clears authentication state
   */
  logout: async () => {
    try {
      set({ loading: true, error: null });

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error("Logout error:", error);
        throw error;
      }

      // Clear local state
      set({
        isAuthenticated: false,
        admin: null,
        loading: false,
      });

      console.log("✅ Successfully logged out");
    } catch (error: any) {
      console.error("Logout error:", error);
      set({
        isAuthenticated: false,
        admin: null,
        loading: false,
        error: error.message || "Logout failed",
      });
      // Even if there's an error, we should clear the local state
    }
  },

  /**
   * Updates the current admin's profile information
   * @param updates - Partial admin data to update
   */
  updateAdmin: async (updates) => {
    try {
      const currentAdmin = get().admin;
      if (!currentAdmin) throw new Error("No admin logged in");

      set({ loading: true, error: null });

      const apiUpdates = transformAdminToApi(updates);
      const updatedApiAdmin = await apiService.updateAdmin(
        currentAdmin.id,
        apiUpdates
      );
      const updatedAdmin = transformAdmin(updatedApiAdmin);

      set({
        admin: updatedAdmin,
        loading: false,
      });
    } catch (error: any) {
      console.error("Update admin error:", error);
      set({
        loading: false,
        error: error.message || "Failed to update profile",
      });
      throw error;
    }
  },
}));

// Listen for auth state changes from Supabase
supabase.auth.onAuthStateChange((event, session) => {
  const store = useAuthStore.getState();

  if (event === "SIGNED_OUT" || !session) {
    // Clear authentication state on sign out
    useAuthStore.setState({
      isAuthenticated: false,
      admin: null,
    });
  } else if (event === "SIGNED_IN" && session) {
    // Load admin profile on sign in
    store.checkAuth();
  }
});

export default useAuthStore;
