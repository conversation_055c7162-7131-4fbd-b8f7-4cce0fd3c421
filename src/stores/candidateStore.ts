import { create } from "zustand";
import { apiService } from "../services/api";
import { transformCandidate } from "../utils/transformers";
import { Candidate, FilterOptions, ApplicationStatus } from "../types";
import useAuthStore from "./authStore";

/**
 * Default filter options for candidates
 */
const defaultFilterOptions: FilterOptions = {
  position: "",
  availability: [],
  storeLocation: "",
  status: [],
  search: "",
};

/**
 * Filters candidates based on provided filter options
 * @param candidates - Array of candidates to filter
 * @param filterOptions - Filter criteria to apply
 * @returns Filtered array of candidates
 */
const filterCandidates = (
  candidates: Candidate[],
  filterOptions: FilterOptions
): Candidate[] => {
  return candidates.filter((candidate) => {
    // Search filter - check name, position, and email
    if (filterOptions.search) {
      const searchValue = filterOptions.search.toLowerCase();
      const fullName =
        `${candidate.firstName} ${candidate.lastName}`.toLowerCase();

      if (
        !fullName.includes(searchValue) &&
        !candidate.position.toLowerCase().includes(searchValue) &&
        !candidate.email.toLowerCase().includes(searchValue)
      ) {
        return false;
      }
    }

    // Position filter
    if (
      filterOptions.position &&
      candidate.position !== filterOptions.position
    ) {
      return false;
    }

    // Availability filter - check if candidate has any matching availability
    if (filterOptions.availability.length > 0) {
      const hasMatchingAvailability = candidate.availability.some((avail) =>
        filterOptions.availability.includes(avail)
      );
      if (!hasMatchingAvailability) return false;
    }

    // Store location filter
    if (
      filterOptions.storeLocation &&
      candidate.storeLocation !== filterOptions.storeLocation
    ) {
      return false;
    }

    // Status filter - check if candidate status is in selected statuses
    if (
      filterOptions.status.length > 0 &&
      !filterOptions.status.includes(candidate.status)
    ) {
      return false;
    }

    return true;
  });
};

/**
 * Candidate store state interface
 */
interface CandidateState {
  // State
  candidates: Candidate[];
  filteredCandidates: Candidate[];
  filterOptions: FilterOptions;
  loading: boolean;
  error: string | null;

  // Actions
  fetchCandidates: () => Promise<void>;
  getCandidate: (id: string) => Promise<Candidate | null>;
  updateCandidate: (id: string, updates: Partial<Candidate>) => Promise<void>;
  addNote: (candidateId: string, content: string) => Promise<void>;
  toggleFlag: (id: string) => Promise<void>;
  updateStatus: (id: string, status: ApplicationStatus) => Promise<void>;
  setFilterOptions: (options: Partial<FilterOptions>) => void;
  resetFilters: () => void;
  clearError: () => void;
}

const useCandidateStore = create<CandidateState>((set, get) => ({
  candidates: [],
  filteredCandidates: [],
  filterOptions: defaultFilterOptions,
  loading: false,
  error: null,

  clearError: () => set({ error: null }),

  /**
   * Fetches all candidates with their notes and timeline events
   */
  fetchCandidates: async () => {
    try {
      console.log("🚀 Starting candidate fetch...");
      set({ loading: true, error: null });

      const apiCandidates = await apiService.getCandidates();
      console.log("📦 Raw API candidates:", apiCandidates.length);

      if (apiCandidates.length === 0) {
        console.log("⚠️ No candidates found in database");
        set({
          candidates: [],
          filteredCandidates: [],
          loading: false,
        });
        return;
      }

      console.log("🔄 Processing candidates with details...");
      // Get notes and timeline events for each candidate
      const candidatesWithDetails = await Promise.all(
        apiCandidates.map(async (apiCandidate) => {
          try {
            const [notes, timelineEvents] = await Promise.all([
              apiService.getNotes(apiCandidate.id),
              apiService.getTimelineEvents(apiCandidate.id),
            ]);

            return transformCandidate(apiCandidate, notes, timelineEvents);
          } catch (error) {
            console.warn(
              `Failed to fetch details for candidate ${apiCandidate.id}:`,
              error
            );
            // Return candidate with empty notes and timeline if details fail
            return transformCandidate(apiCandidate, [], []);
          }
        })
      );

      console.log("✨ Processed candidates:", candidatesWithDetails.length);
      console.log("🎯 Sample processed candidate:", candidatesWithDetails[0]);
      const filteredCandidates = filterCandidates(
        candidatesWithDetails,
        get().filterOptions
      );
      console.log("🔍 Filtered candidates:", filteredCandidates.length);

      set({
        candidates: candidatesWithDetails,
        filteredCandidates,
        loading: false,
      });
    } catch (error: any) {
      console.error("💥 Candidate fetch failed:", error);
      const errorMessage = error?.message || "Failed to fetch candidates";
      set({
        candidates: [],
        filteredCandidates: [],
        error: errorMessage,
        loading: false,
      });
    }
  },

  /**
   * Retrieves a single candidate by ID with complete details
   * @param id - Candidate ID
   * @returns Candidate with details or null if not found
   */
  getCandidate: async (id: string) => {
    try {
      const [apiCandidate, notes, timelineEvents] = await Promise.all([
        apiService.getCandidate(id),
        apiService.getNotes(id),
        apiService.getTimelineEvents(id),
      ]);

      return transformCandidate(apiCandidate, notes, timelineEvents);
    } catch (error: any) {
      console.error("Get candidate error:", error);
      set({ error: error.message || "Failed to fetch candidate" });
      return null;
    }
  },

  /**
   * Updates candidate information
   * @param id - Candidate ID
   * @param updates - Partial candidate data to update
   */
  updateCandidate: async (id: string, updates: Partial<Candidate>) => {
    try {
      set({ loading: true, error: null });

      const apiUpdates = transformCandidateToApi(updates);
      const updatedApiCandidate = await apiService.updateCandidate(
        id,
        apiUpdates
      );

      // Get updated notes and timeline
      const [notes, timelineEvents] = await Promise.all([
        apiService.getNotes(id),
        apiService.getTimelineEvents(id),
      ]);

      const updatedCandidate = transformCandidate(
        updatedApiCandidate,
        notes,
        timelineEvents
      );

      const candidates = get().candidates.map((c) =>
        c.id === id ? updatedCandidate : c
      );

      set({
        candidates,
        filteredCandidates: filterCandidates(candidates, get().filterOptions),
        loading: false,
      });
    } catch (error: any) {
      console.error("Update candidate error:", error);
      set({
        error: error.message || "Failed to update candidate",
        loading: false,
      });
      throw error;
    }
  },

  addNote: async (candidateId: string, content: string) => {
    try {
      const { admin } = useAuthStore.getState();
      if (!admin) throw new Error("No admin logged in");

      const adminId = admin.id;
      await apiService.createNote(candidateId, content, adminId);
      await get().fetchCandidates(); // Refresh data
    } catch (error: any) {
      console.error("Add note error:", error);
      set({ error: error.message || "Failed to add note" });
      throw error;
    }
  },

  toggleFlag: async (id: string) => {
    try {
      const candidate = get().candidates.find((c) => c.id === id);
      if (!candidate) return;

      await get().updateCandidate(id, { flagged: !candidate.flagged });
    } catch (error: any) {
      console.error("Toggle flag error:", error);
      set({ error: error.message || "Failed to toggle flag" });
      throw error;
    }
  },

  updateStatus: async (id: string, status: ApplicationStatus) => {
    try {
      await apiService.updateCandidate(id, { status });
      const { admin } = useAuthStore.getState();
      if (!admin) throw new Error("No admin logged in");

      const adminId = admin.id;
      await apiService.createTimelineEvent(
        id,
        adminId,
        "Status Updated",
        `Status changed to ${status}`,
        "status_change"
      );
      await get().fetchCandidates(); // Refresh data
    } catch (error: any) {
      console.error("Update status error:", error);
      set({ error: error.message || "Failed to update status" });
      throw error;
    }
  },

  setFilterOptions: (options: Partial<FilterOptions>) => {
    const updatedOptions = { ...get().filterOptions, ...options };
    const filteredCandidates = filterCandidates(
      get().candidates,
      updatedOptions
    );
    set({ filterOptions: updatedOptions, filteredCandidates });
  },

  resetFilters: () => {
    set({
      filterOptions: defaultFilterOptions,
      filteredCandidates: filterCandidates(
        get().candidates,
        defaultFilterOptions
      ),
    });
  },
}));

const transformCandidateToApi = (
  candidate: Partial<Candidate>
): Partial<any> => {
  const apiCandidate: Partial<any> = {};

  if (candidate.firstName) apiCandidate.first_name = candidate.firstName;
  if (candidate.lastName) apiCandidate.last_name = candidate.lastName;
  if (candidate.email) apiCandidate.email = candidate.email;
  if (candidate.phone) apiCandidate.phone = candidate.phone;
  if (candidate.position) apiCandidate.position = candidate.position;
  if (candidate.storeLocation)
    apiCandidate.store_location = candidate.storeLocation;
  if (candidate.availability)
    apiCandidate.availability = candidate.availability;
  if (candidate.resume) apiCandidate.resume_url = candidate.resume;
  if (candidate.status) apiCandidate.status = candidate.status;
  if (candidate.flagged !== undefined) apiCandidate.flagged = candidate.flagged;

  return apiCandidate;
};

export default useCandidateStore;
