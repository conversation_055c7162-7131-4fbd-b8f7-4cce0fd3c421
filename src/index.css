@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-neutral-50 font-sans text-black;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans;
  }
  
  h1 {
    @apply text-3xl font-bold;
  }
  
  h2 {
    @apply text-2xl font-bold;
  }
  
  h3 {
    @apply text-xl font-bold;
  }
  
  h4 {
    @apply text-lg font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold focus:ring-opacity-50;
  }
  
  .btn-primary {
    @apply bg-gold text-white hover:bg-gold-dark;
  }
  
  .btn-secondary {
    @apply bg-white text-black border border-neutral-300 hover:bg-neutral-50;
  }
  
  .btn-outline {
    @apply bg-transparent text-gold border border-gold hover:bg-gold hover:text-white;
  }
  
  .input {
    @apply w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent transition-colors duration-200;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gold;
}

/* Form elements */
.form-checkbox {
  @apply w-4 h-4 text-gold border-neutral-300 rounded focus:ring-gold focus:ring-2;
}

/* Micro-interactions */
.btn:active {
  transform: translateY(1px);
}

.card-hover:hover {
  transform: translateY(-2px);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}