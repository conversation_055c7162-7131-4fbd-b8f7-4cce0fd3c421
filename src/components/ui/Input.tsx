import React, { forwardRef } from "react";

/**
 * Props for the Input component
 * Extends standard HTML input attributes
 */
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /** Optional label text */
  label?: string;
  /** Error message to display */
  error?: string;
  /** Optional icon to display inside input */
  icon?: React.ReactNode;
  /** Helper text to display below input */
  helperText?: string;
}

/**
 * Reusable input component with label, error states, and icon support
 * Provides consistent styling and accessibility features
 */
const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, icon, helperText, className = "", ...props }, ref) => {
    const inputClasses = `
    w-full px-4 py-2 border rounded-md transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent
    ${error ? "border-error" : "border-neutral-300"}
    ${icon ? "pl-10" : ""}
    ${className}
  `;

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-neutral-700 mb-1">
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-neutral-400">{icon}</span>
            </div>
          )}
          <input ref={ref} className={inputClasses} {...props} />
        </div>
        {error && <p className="mt-1 text-sm text-error">{error}</p>}
        {helperText && !error && (
          <p className="mt-1 text-sm text-neutral-500">{helperText}</p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
