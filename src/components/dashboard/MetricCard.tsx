import { ReactNode, memo, useCallback } from "react";
import { useNavigate } from "react-router-dom";

/**
 * Props for the MetricCard component
 */
interface MetricCardProps {
  /** Title text for the metric */
  title: string;
  /** Numeric value to display */
  value: number;
  /** Icon element to display */
  icon: ReactNode;
  /** Background color class for the icon */
  color?: string;
  /** Optional status for filtering when clicked */
  status?: string;
}

/**
 * Clickable metric card component for dashboard
 * Displays a metric with icon and navigates to candidates page when clicked
 */
const MetricCard = memo(
  ({ title, value, icon, color = "bg-gold", status }: MetricCardProps) => {
    const navigate = useNavigate();

    /**
     * Handles card click navigation to candidates page with optional filter
     */
    const handleClick = useCallback(() => {
      navigate("/candidates", {
        state: { filterStatus: status },
      });
    }, [navigate, status]);

    /**
     * <PERSON>les keyboard navigation for accessibility
     */
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleClick();
        }
      },
      [handleClick]
    );

    return (
      <div
        className="bg-white rounded-lg shadow-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer"
        onClick={handleClick}
        role="button"
        tabIndex={0}
        onKeyDown={handleKeyDown}
      >
        <div className="p-6">
          <div className="flex items-center">
            <div
              className={`p-3 rounded-lg ${color} bg-opacity-10 text-gold-dark transition-transform duration-300 group-hover:scale-110`}
            >
              {icon}
            </div>
            <div className="ml-5">
              <p className="text-sm font-medium text-neutral-500">{title}</p>
              <div className="flex items-end">
                <p className="text-2xl font-semibold text-black">{value}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default MetricCard;
