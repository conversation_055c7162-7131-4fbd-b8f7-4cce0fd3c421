import { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { ApplicationStatus, Availability, FilterOptions } from '../../types';
import { POSITIONS, AVAILABILITY_OPTIONS, STORE_LOCATIONS, STATUS_OPTIONS } from '../../config/constants';

interface CandidateFilterProps {
  filterOptions: FilterOptions;
  onFilterChange: (options: Partial<FilterOptions>) => void;
  onReset: () => void;
}

const CandidateFilter = ({ filterOptions, onFilterChange, onReset }: CandidateFilterProps) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange({ search: e.target.value });
  };
  
  const handleAvailabilityChange = (availability: Availability) => {
    const currentAvailability = [...filterOptions.availability];
    
    if (currentAvailability.includes(availability)) {
      onFilterChange({
        availability: currentAvailability.filter(a => a !== availability)
      });
    } else {
      onFilterChange({
        availability: [...currentAvailability, availability]
      });
    }
  };
  
  const handleStatusChange = (status: ApplicationStatus) => {
    const currentStatus = [...filterOptions.status];
    
    if (currentStatus.includes(status)) {
      onFilterChange({
        status: currentStatus.filter(s => s !== status)
      });
    } else {
      onFilterChange({
        status: [...currentStatus, status]
      });
    }
  };
  
  const hasActiveFilters = 
    filterOptions.position !== '' || 
    filterOptions.availability.length > 0 || 
    filterOptions.storeLocation !== '' || 
    filterOptions.status.length > 0;
  
  return (
    <div className="mb-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-neutral-400" />
          </div>
          <input
            type="text"
            className="pl-10 input w-full"
            placeholder="Search candidates..."
            value={filterOptions.search}
            onChange={handleSearchChange}
          />
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="btn btn-secondary flex items-center gap-2"
          >
            <Filter size={16} />
            Filters
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={onReset}
              className="btn btn-secondary text-error flex items-center gap-2"
            >
              <X size={16} />
              Reset
            </button>
          )}
        </div>
      </div>
      
      {isFilterOpen && (
        <div className="mt-4 p-4 bg-white rounded-lg shadow-sm border border-neutral-200 animate-fade-in">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {/* Position filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Position</h4>
              <select
                className="input w-full"
                value={filterOptions.position}
                onChange={(e) => onFilterChange({ position: e.target.value })}
              >
                <option value="">All Positions</option>
                {POSITIONS.map((position) => (
                  <option key={position} value={position}>
                    {position}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Availability filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Availability</h4>
              <div className="space-y-1">
                {AVAILABILITY_OPTIONS.map((availability) => (
                  <label key={availability} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      className="form-checkbox rounded text-gold focus:ring-gold"
                      checked={filterOptions.availability.includes(availability)}
                      onChange={() => handleAvailabilityChange(availability)}
                    />
                    <span className="text-sm">{availability}</span>
                  </label>
                ))}
              </div>
            </div>
            
            {/* Store Location filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Store Location</h4>
              <select
                className="input w-full"
                value={filterOptions.storeLocation}
                onChange={(e) => onFilterChange({ storeLocation: e.target.value })}
              >
                <option value="">All Locations</option>
                {STORE_LOCATIONS.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Status filter */}
            <div>
              <h4 className="font-medium text-sm mb-2">Status</h4>
              <div className="space-y-1">
                {STATUS_OPTIONS.map((status) => (
                  <label key={status.value} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      className="form-checkbox rounded text-gold focus:ring-gold"
                      checked={filterOptions.status.includes(status.value as ApplicationStatus)}
                      onChange={() => handleStatusChange(status.value as ApplicationStatus)}
                    />
                    <span className="text-sm">{status.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CandidateFilter;