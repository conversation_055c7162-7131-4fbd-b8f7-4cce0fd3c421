import { useState } from 'react';
import { Save, X } from 'lucide-react';
import { Candidate, Availability } from '../../types';
import { POSITIONS, STORE_LOCATIONS, AVAILABILITY_OPTIONS } from '../../config/constants';
import { validationService } from '../../services/validationService';

interface CandidateEditFormProps {
  candidate: Candidate;
  onSave: (updates: Partial<Candidate>) => Promise<Candidate | null>;
  onCancel: () => void;
}

const CandidateEditForm = ({ candidate, onSave, onCancel }: CandidateEditFormProps) => {
  const [editData, setEditData] = useState({
    firstName: candidate.firstName,
    lastName: candidate.lastName,
    email: candidate.email,
    phone: candidate.phone,
    position: candidate.position,
    storeLocation: candidate.storeLocation,
    availability: candidate.availability
  });
  
  const [errors, setErrors] = useState<string[]>([]);
  const [saving, setSaving] = useState(false);

  const handleAvailabilityChange = (availability: Availability) => {
    const currentAvailability = [...editData.availability];
    
    if (currentAvailability.includes(availability)) {
      setEditData({
        ...editData,
        availability: currentAvailability.filter(a => a !== availability)
      });
    } else {
      setEditData({
        ...editData,
        availability: [...currentAvailability, availability]
      });
    }
  };

  const handleSave = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const validation = validationService.validateCandidateUpdate(editData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    
    setSaving(true);
    setErrors([]);
    
    try {
      await onSave(editData);
    } catch (error) {
      setErrors(['Failed to save changes']);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCancel();
  };

  return (
    <div className="space-y-3" onClick={(e) => e.stopPropagation()}>
      {errors.length > 0 && (
        <div className="text-xs text-error bg-error/10 p-2 rounded">
          {errors.join(', ')}
        </div>
      )}
      
      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          value={editData.firstName}
          onChange={(e) => setEditData({ ...editData, firstName: e.target.value })}
          className="input text-sm py-1"
          placeholder="First Name"
        />
        <input
          type="text"
          value={editData.lastName}
          onChange={(e) => setEditData({ ...editData, lastName: e.target.value })}
          className="input text-sm py-1"
          placeholder="Last Name"
        />
      </div>
      
      <input
        type="email"
        value={editData.email}
        onChange={(e) => setEditData({ ...editData, email: e.target.value })}
        className="input text-sm py-1 w-full"
        placeholder="Email"
      />
      
      <input
        type="tel"
        value={editData.phone}
        onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
        className="input text-sm py-1 w-full"
        placeholder="Phone"
      />
      
      <select
        value={editData.position}
        onChange={(e) => setEditData({ ...editData, position: e.target.value })}
        className="input text-sm py-1 w-full"
      >
        {POSITIONS.map((position) => (
          <option key={position} value={position}>
            {position}
          </option>
        ))}
      </select>
      
      <select
        value={editData.storeLocation}
        onChange={(e) => setEditData({ ...editData, storeLocation: e.target.value })}
        className="input text-sm py-1 w-full"
      >
        {STORE_LOCATIONS.map((location) => (
          <option key={location} value={location}>
            {location}
          </option>
        ))}
      </select>
      
      <div>
        <p className="text-xs text-neutral-500 mb-2">Availability</p>
        <div className="flex flex-wrap gap-1">
          {AVAILABILITY_OPTIONS.map((availability) => (
            <button
              key={availability}
              type="button"
              onClick={() => handleAvailabilityChange(availability)}
              className={`px-2 py-1 text-xs rounded-full transition-colors ${
                editData.availability.includes(availability)
                  ? 'bg-gold text-white'
                  : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
              }`}
            >
              {availability}
            </button>
          ))}
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-2">
        <button
          onClick={handleSave}
          disabled={saving}
          className="text-success hover:text-success/80 transition-colors duration-200 flex items-center"
          aria-label="Save changes"
        >
          <Save size={16} />
          {saving && <span className="ml-1 text-xs">Saving...</span>}
        </button>
        <button
          onClick={handleCancel}
          className="text-error hover:text-error/80 transition-colors duration-200"
          aria-label="Cancel editing"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default CandidateEditForm;