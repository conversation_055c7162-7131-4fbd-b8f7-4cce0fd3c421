import { useState, useEffect, Suspense, lazy } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import useAuthStore from "./stores/authStore";
import LoadingSpinner from "./components/ui/LoadingSpinner";
import ErrorBoundary from "./components/ui/ErrorBoundary";

// Lazy load pages for code splitting
const LoginPage = lazy(() => import("./pages/LoginPage"));
const ApplicationForm = lazy(() => import("./pages/ApplicationForm"));
const DashboardPage = lazy(() => import("./pages/DashboardPage"));
const CandidatesPage = lazy(() => import("./pages/CandidatesPage"));
const CandidateDetails = lazy(() => import("./pages/CandidateDetails"));
const Settings = lazy(() => import("./pages/Settings"));
const NotFound = lazy(() => import("./pages/NotFound"));

function App() {
  const [loading, setLoading] = useState(true);
  const { isAuthenticated, checkAuth } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      await checkAuth();
      setLoading(false);
    };

    initializeAuth();
  }, [checkAuth]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/apply" element={<ApplicationForm />} />
          <Route
            path="/login"
            element={
              isAuthenticated ? <Navigate to="/dashboard" /> : <LoginPage />
            }
          />
          <Route
            path="/dashboard"
            element={
              isAuthenticated ? <DashboardPage /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/candidates"
            element={
              isAuthenticated ? <CandidatesPage /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/candidates/:id"
            element={
              isAuthenticated ? <CandidateDetails /> : <Navigate to="/login" />
            }
          />
          <Route
            path="/settings"
            element={isAuthenticated ? <Settings /> : <Navigate to="/login" />}
          />
          <Route
            path="/"
            element={
              <Navigate to={isAuthenticated ? "/dashboard" : "/login"} />
            }
          />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </AnimatePresence>
    </ErrorBoundary>
  );
}

export default App;
