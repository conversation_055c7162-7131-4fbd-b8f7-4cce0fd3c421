import { useState, useCallback } from 'react';

interface AsyncOperationState {
  loading: boolean;
  error: string | null;
}

export const useAsyncOperation = () => {
  const [state, setState] = useState<AsyncOperationState>({
    loading: false,
    error: null
  });

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    errorMessage?: string
  ): Promise<T | null> => {
    try {
      setState({ loading: true, error: null });
      const result = await operation();
      setState({ loading: false, error: null });
      return result;
    } catch (err: any) {
      const message = err?.message || errorMessage || 'An error occurred';
      setState({ loading: false, error: message });
      console.error('Async operation failed:', err);
      return null;
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    loading: state.loading,
    error: state.error,
    execute,
    clearError
  };
};