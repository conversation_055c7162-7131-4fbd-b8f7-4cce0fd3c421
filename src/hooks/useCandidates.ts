import { useState, useEffect, useCallback } from 'react';
import { candidateService } from '../services/candidateService';
import { filterService } from '../services/filterService';
import { Candidate, FilterOptions, ApplicationStatus } from '../types';
import { useAsyncOperation } from './useAsyncOperation';

export const useCandidates = () => {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [filteredCandidates, setFilteredCandidates] = useState<Candidate[]>([]);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>(
    filterService.createDefaultFilters()
  );
  
  const { loading, error, execute, clearError } = useAsyncOperation();

  const fetchCandidates = useCallback(async () => {
    const result = await execute(
      () => candidateService.getAllCandidates(),
      'Failed to fetch candidates'
    );
    
    if (result) {
      setCandidates(result);
      setFilteredCandidates(filterService.filterCandidates(result, filterOptions));
    }
  }, [execute, filterOptions]);

  const updateCandidate = useCallback(async (id: string, updates: Partial<Candidate>) => {
    const result = await execute(
      () => candidateService.updateCandidate(id, updates),
      'Failed to update candidate'
    );
    
    if (result) {
      const updatedCandidates = candidates.map(c => c.id === id ? result : c);
      setCandidates(updatedCandidates);
      setFilteredCandidates(filterService.filterCandidates(updatedCandidates, filterOptions));
    }
    
    return result;
  }, [candidates, filterOptions, execute]);

  const addNote = useCallback(async (candidateId: string, content: string) => {
    const result = await execute(
      () => candidateService.addNote(candidateId, content),
      'Failed to add note'
    );
    
    if (result !== null) {
      await fetchCandidates(); // Refresh data
    }
    
    return result;
  }, [execute, fetchCandidates]);

  const toggleFlag = useCallback(async (id: string) => {
    const candidate = candidates.find(c => c.id === id);
    if (!candidate) return null;
    
    const result = await execute(
      () => candidateService.toggleFlag(id, candidate.flagged),
      'Failed to toggle flag'
    );
    
    if (result !== null) {
      const updatedCandidates = candidates.map(c => 
        c.id === id ? { ...c, flagged: !c.flagged } : c
      );
      setCandidates(updatedCandidates);
      setFilteredCandidates(filterService.filterCandidates(updatedCandidates, filterOptions));
    }
    
    return result;
  }, [candidates, filterOptions, execute]);

  const updateStatus = useCallback(async (id: string, status: ApplicationStatus) => {
    const result = await execute(
      () => candidateService.updateStatus(id, status),
      'Failed to update status'
    );
    
    if (result !== null) {
      await fetchCandidates(); // Refresh data
    }
    
    return result;
  }, [execute, fetchCandidates]);

  const updateFilters = useCallback((newFilters: Partial<FilterOptions>) => {
    const updatedFilters = { ...filterOptions, ...newFilters };
    setFilterOptions(updatedFilters);
    setFilteredCandidates(filterService.filterCandidates(candidates, updatedFilters));
  }, [candidates, filterOptions]);

  const resetFilters = useCallback(() => {
    const defaultFilters = filterService.createDefaultFilters();
    setFilterOptions(defaultFilters);
    setFilteredCandidates(filterService.filterCandidates(candidates, defaultFilters));
  }, [candidates]);

  useEffect(() => {
    fetchCandidates();
  }, []);

  return {
    candidates: filteredCandidates,
    allCandidates: candidates,
    filterOptions,
    loading,
    error,
    fetchCandidates,
    updateCandidate,
    addNote,
    toggleFlag,
    updateStatus,
    updateFilters,
    resetFilters,
    clearError
  };
};