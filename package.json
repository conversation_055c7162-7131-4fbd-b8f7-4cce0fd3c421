{"name": "skin-spa-hiring-portal", "private": true, "version": "0.4.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@lucide/lab": "^0.1.2", "@supabase/supabase-js": "^2.39.7", "date-fns": "^3.3.1", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4"}}